{% extends 'app/base.html' %}

{% block content %}
<h2 class="page-title">My Journal Entries</h2>

<div class="search-form">
    <form method="get" action="{% url 'journal_search' %}">
        <input type="text" name="q" placeholder="Search entries..." value="{{ query|default:'' }}">
        <button type="submit"><i class="fas fa-search animated-icon"></i> Search</button>
    </form>
</div>

<div class="journal-header">
    <p><i class="fas fa-book-open animated-icon"></i> Your personal space for thoughts and memories</p>
    <a href="{% url 'journal_new' %}" class="button pulse-button">
        <!-- Animated Open Book SVG -->
        <svg viewBox="0 0 512 512" width="20" height="20" class="button-icon book-icon">
            <rect x="50" y="100" width="412" height="312" rx="30" ry="30" fill="#34495E" />
            <path d="M256 120 L256 400 L80 380 L80 140 Z" fill="#F5F5F5" />
            <path d="M256 120 L256 400 L432 380 L432 140 Z" fill="#E0E0E0" />
            <line x1="256" y1="120" x2="256" y2="400" stroke="#90A4AE" stroke-width="8" />
            <line x1="100" y1="160" x2="236" y2="160" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="190" x2="236" y2="190" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="220" x2="236" y2="220" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="250" x2="236" y2="250" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="280" x2="236" y2="280" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="310" x2="236" y2="310" stroke="#BDBDBD" stroke-width="6" />
            <line x1="100" y1="340" x2="236" y2="340" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="160" x2="412" y2="160" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="190" x2="412" y2="190" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="220" x2="412" y2="220" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="250" x2="412" y2="250" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="280" x2="412" y2="280" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="310" x2="412" y2="310" stroke="#BDBDBD" stroke-width="6" />
            <line x1="276" y1="340" x2="412" y2="340" stroke="#BDBDBD" stroke-width="6" />
        </svg>
        New Entry
    </a>
</div>

<div class="entry-list">
    {% for entry in entries %}
    <div class="entry-card">
        <div class="entry-image" onclick="openModal('{% if entry.image %}{{ entry.image.url }}{% else %}/static/images/default-entry.jpg{% endif %}')">
            {% if entry.image %}
            <img src="{{ entry.image.url }}" alt="{{ entry.title }}">
            {% else %}
            <img src="/static/images/default-entry.jpg" alt="Default image">
            {% endif %}
        </div>
        <div class="entry-content">
            <h3 class="entry-title">{{ entry.title }}</h3>
            <div class="entry-date">
                <i class="far fa-calendar-alt animated-icon"></i> {{ entry.created_at|date:"F j, Y" }}
            </div>
            <p>{{ entry.content|truncatewords:20 }}</p>
            <div class="entry-actions">
                <a href="{% url 'journal_detail' pk=entry.pk %}" class="button"><i class="fas fa-book-reader animated-icon"></i> Read More</a>
            </div>
            {% if entry.tags %}
            <div class="entry-tags">
                {% for tag in entry.tags.split|slice:":3" %}
                <span class="tag"><i class="fas fa-tag animated-icon"></i> {{ tag }}</span>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
    {% empty %}
    <div class="empty-state">
        <!-- Animated Empty Journal SVG -->
        <svg viewBox="0 0 512 512" width="150" height="150">
            <rect x="100" y="80" width="312" height="352" rx="20" ry="20" fill="#ECEFF1" />
            <path d="M256 100 L256 412 L120 400 L120 112 Z" fill="#F5F5F5" />
            <path d="M256 100 L256 412 L392 400 L392 112 Z" fill="#E0E0E0" />
            <line x1="256" y1="100" x2="256" y2="412" stroke="#90A4AE" stroke-width="4" />
            <path d="M180 250 C180 220 220 220 220 250 C220 280 180 280 180 250 Z" fill="#BBDEFB" opacity="0.5" />
            <path d="M332 250 C332 220 372 220 372 250 C372 280 332 280 332 250 Z" fill="#BBDEFB" opacity="0.5" />
            <path d="M200 300 L312 300" stroke="#BBDEFB" stroke-width="8" stroke-linecap="round" opacity="0.5" />
        </svg>
        <p>You haven't created any journal entries yet.</p>
        <a href="{% url 'journal_new' %}" class="button pulse-button">
            <!-- Animated Open Book SVG -->
            <svg viewBox="0 0 512 512" width="20" height="20" class="button-icon book-icon">
                <rect x="50" y="100" width="412" height="312" rx="30" ry="30" fill="#34495E" />
                <path d="M256 120 L256 400 L80 380 L80 140 Z" fill="#F5F5F5" />
                <path d="M256 120 L256 400 L432 380 L432 140 Z" fill="#E0E0E0" />
                <line x1="256" y1="120" x2="256" y2="400" stroke="#90A4AE" stroke-width="8" />
                <line x1="100" y1="160" x2="236" y2="160" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="190" x2="236" y2="190" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="220" x2="236" y2="220" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="250" x2="236" y2="250" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="280" x2="236" y2="280" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="310" x2="236" y2="310" stroke="#BDBDBD" stroke-width="6" />
                <line x1="100" y1="340" x2="236" y2="340" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="160" x2="412" y2="160" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="190" x2="412" y2="190" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="220" x2="412" y2="220" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="250" x2="412" y2="250" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="280" x2="412" y2="280" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="310" x2="412" y2="310" stroke="#BDBDBD" stroke-width="6" />
                <line x1="276" y1="340" x2="412" y2="340" stroke="#BDBDBD" stroke-width="6" />
            </svg>
            Create Your First Entry
        </a>
    </div>
    {% endfor %}
</div>
{% endblock %}




