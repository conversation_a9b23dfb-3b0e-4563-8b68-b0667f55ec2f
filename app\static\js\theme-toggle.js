// Enhanced Theme Toggle with Smooth Transitions
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.init();
    }

    init() {
        this.setTheme(this.currentTheme);
        this.createToggleButton();
        this.bindEvents();
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        this.updateToggleButton();
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        this.animateToggle();
    }

    createToggleButton() {
        const existingToggle = document.querySelector('.theme-toggle');
        if (existingToggle) return;

        const toggle = document.createElement('div');
        toggle.className = 'theme-toggle';
        toggle.innerHTML = `
            <div class="theme-toggle-slider">
                <i class="fas fa-moon"></i>
            </div>
        `;

        // Add to navigation
        const nav = document.querySelector('.nav-container');
        if (nav) {
            nav.appendChild(toggle);
        }
    }

    updateToggleButton() {
        const slider = document.querySelector('.theme-toggle-slider i');
        if (slider) {
            slider.className = this.currentTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    animateToggle() {
        const slider = document.querySelector('.theme-toggle-slider');
        if (slider) {
            slider.style.transform = 'scale(0.8)';
            setTimeout(() => {
                slider.style.transform = '';
            }, 150);
        }

        // Add ripple effect
        this.createRipple();
    }

    createRipple() {
        const toggle = document.querySelector('.theme-toggle');
        if (!toggle) return;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
            width: 100px;
            height: 100px;
            left: 50%;
            top: 50%;
            margin-left: -50px;
            margin-top: -50px;
        `;

        toggle.style.position = 'relative';
        toggle.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.theme-toggle')) {
                this.toggleTheme();
            }
        });

        // Keyboard support
        document.addEventListener('keydown', (e) => {
            if (e.key === 't' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }
}

// Add ripple animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ThemeManager();
});

// Auto-detect system theme preference
if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
            const theme = e.matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', theme);
        }
    });
}
