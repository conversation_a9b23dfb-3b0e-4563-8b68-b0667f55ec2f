{% extends 'app/base.html' %}

{% block content %}
<link rel="stylesheet" href="/static/css/calendar.css">

<div class="calendar-container">
<div class="calendar-wrapper">
    <div class="calendar-header">
        <div class="calendar-navigation">
            <a href="{% url 'calendar_month' prev_year prev_month %}" class="nav-btn">
                <i class="fas fa-chevron-left"></i>
            </a>
            <h2 class="calendar-title">
                <i class="fas fa-calendar-alt animated-icon"></i>
                {{ month_name }} {{ year }}
            </h2>
            <a href="{% url 'calendar_month' next_year next_month %}" class="nav-btn">
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>

        <div class="calendar-actions">
            <a href="{% url 'journal_new' %}" class="button primary-btn">
                <i class="fas fa-plus animated-icon"></i> New Entry
            </a>
            <a href="{% url 'calendar_view' %}" class="button secondary">
                <i class="fas fa-home animated-icon"></i> Today
            </a>
        </div>
    </div>

    <div class="calendar-grid">
        <!-- Day headers -->
        <div class="calendar-weekdays">
            <div class="weekday">Sun</div>
            <div class="weekday">Mon</div>
            <div class="weekday">Tue</div>
            <div class="weekday">Wed</div>
            <div class="weekday">Thu</div>
            <div class="weekday">Fri</div>
            <div class="weekday">Sat</div>
        </div>

        <!-- Calendar days -->
        <div class="calendar-days">
            {% for week in calendar %}
                {% for day in week %}
                    {% if day == 0 %}
                        <div class="calendar-day empty"></div>
                    {% else %}
                        <div class="calendar-day {% if day == today.day and month == today.month and year == today.year %}today{% endif %} {% if day in entries_by_day %}has-entries{% endif %}"
                             data-day="{{ day }}" data-month="{{ month }}" data-year="{{ year }}">
                            <div class="day-number">{{ day }}</div>

                            {% if day in entries_by_day %}
                                <div class="day-entries">
                                    {% for entry in entries_by_day|lookup:day %}
                                        <div class="entry-indicator" title="{{ entry.title }}">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                    {% endfor %}
                                    {% if entries_by_day|lookup:day|length > 3 %}
                                        <div class="more-entries">+{{ entries_by_day|lookup:day|length|add:"-3" }}</div>
                                    {% endif %}
                                </div>
                            {% endif %}

                            <div class="day-actions">
                                <a href="{% url 'calendar_day' year month day %}" class="view-day" title="View day details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'journal_new' %}?date={{ year }}-{{ month|stringformat:'02d' }}-{{ day|stringformat:'02d' }}"
                                   class="add-entry" title="Add entry for this day">
                                    <i class="fas fa-plus"></i>
                                </a>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endfor %}
        </div>
    </div>

    <!-- Calendar Legend -->
    <div class="calendar-legend">
        <div class="legend-item">
            <div class="legend-color today-color"></div>
            <span>Today</span>
        </div>
        <div class="legend-item">
            <div class="legend-color has-entries-color"></div>
            <span>Has Entries</span>
        </div>
        <div class="legend-item">
            <i class="fas fa-circle entry-indicator-sample"></i>
            <span>Journal Entry</span>
        </div>
    </div>

    <!-- Quick Entry Preview Modal -->
    <div id="entryPreviewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="previewDate"></h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Entry previews will be loaded here -->
            </div>
        </div>
    </div>
</div>
</div>

<style>
.calendar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.calendar-title {
    font-size: 2rem;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.calendar-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.calendar-grid {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--primary-color);
    color: white;
}

.weekday {
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #f0f0f0;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.calendar-day:hover {
    background: #f8f9fa;
    transform: scale(1.02);
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.calendar-day.empty {
    background: #f8f9fa;
    cursor: default;
}

.calendar-day.today {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.calendar-day.today .day-number {
    color: white;
    font-weight: bold;
}

.calendar-day.has-entries {
    border-left: 4px solid var(--accent-color);
}

.day-number {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.day-entries {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    overflow: hidden;
}

.entry-indicator {
    font-size: 0.7rem;
    color: var(--accent-color);
    opacity: 0.8;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.more-entries {
    font-size: 0.7rem;
    color: var(--text-color);
    opacity: 0.6;
    font-style: italic;
}

.day-actions {
    display: flex;
    gap: 5px;
    margin-top: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.calendar-day:hover .day-actions {
    opacity: 1;
}

.view-day, .add-entry {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 4px 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.view-day:hover, .add-entry:hover {
    background: var(--accent-color);
    transform: scale(1.1);
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-color);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.today-color {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.has-entries-color {
    background: var(--accent-color);
}

.entry-indicator-sample {
    color: var(--accent-color);
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.close-modal {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close-modal:hover {
    color: #000;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        text-align: center;
    }

    .calendar-day {
        min-height: 80px;
        padding: 4px;
    }

    .day-number {
        font-size: 0.9rem;
    }

    .calendar-legend {
        gap: 15px;
    }

    .legend-item {
        font-size: 0.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('entryPreviewModal');
    const calendarDays = document.querySelectorAll('.calendar-day[data-day]');
    const closeBtn = document.querySelector('.close-modal');

    // Add click handlers for calendar days
    calendarDays.forEach(day => {
        day.addEventListener('click', function(e) {
            // Don't trigger if clicking on action buttons
            if (e.target.closest('.day-actions')) {
                return;
            }

            const dayNum = this.dataset.day;
            const month = this.dataset.month;
            const year = this.dataset.year;

            // Redirect to day view
            window.location.href = `/calendar/day/${year}/${month}/${dayNum}/`;
        });
    });

    // Close modal
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});
</script>

<!-- Custom filter for template lookup -->
{% load custom_filters %}
{% endblock %}
