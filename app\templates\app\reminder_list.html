{% extends 'app/base.html' %}

{% block content %}
<div class="reminders-container">
    <div class="page-header">
        <h2 class="page-title"><i class="fas fa-bell animated-icon"></i> Journal Reminders</h2>
        <div class="streak-display">
            <div class="streak-card">
                <div class="streak-icon">
                    <i class="fas fa-fire animated-icon"></i>
                </div>
                <div class="streak-info">
                    <h3>{{ streak.current_streak }} Day{% if streak.current_streak != 1 %}s{% endif %}</h3>
                    <p>Current Streak</p>
                </div>
            </div>
            <div class="streak-card">
                <div class="streak-icon">
                    <i class="fas fa-trophy animated-icon"></i>
                </div>
                <div class="streak-info">
                    <h3>{{ streak.longest_streak }} Day{% if streak.longest_streak != 1 %}s{% endif %}</h3>
                    <p>Longest Streak</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="reminders-actions">
        <a href="{% url 'reminder_create' %}" class="button primary-btn">
            <i class="fas fa-plus animated-icon"></i> New Reminder
        </a>
    </div>
    
    <div class="reminders-list">
        {% for reminder in reminders %}
        <div class="reminder-card {% if not reminder.active %}inactive{% endif %}">
            <div class="reminder-header">
                <h3>{{ reminder.title }}</h3>
                <div class="reminder-toggle">
                    <label class="switch">
                        <input type="checkbox" {% if reminder.active %}checked{% endif %} 
                               onchange="toggleReminder({{ reminder.id }}, this.checked)">
                        <span class="slider round"></span>
                    </label>
                </div>
            </div>
            <div class="reminder-details">
                <div class="reminder-time">
                    <i class="far fa-clock animated-icon"></i> {{ reminder.time|time:"g:i A" }}
                </div>
                <div class="reminder-frequency">
                    <i class="fas fa-calendar-alt animated-icon"></i> 
                    {% if reminder.frequency == 'daily' %}
                        Every day
                    {% elif reminder.frequency == 'weekly' %}
                        Weekly on 
                        {% for day in reminder.days.split %}
                            {{ day|title }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    {% else %}
                        Custom schedule
                    {% endif %}
                </div>
            </div>
            <div class="reminder-actions">
                <a href="{% url 'reminder_edit' pk=reminder.pk %}" class="button small">
                    <i class="fas fa-edit animated-icon"></i> Edit
                </a>
                <a href="{% url 'reminder_delete' pk=reminder.pk %}" class="button small danger">
                    <i class="fas fa-trash animated-icon"></i> Delete
                </a>
            </div>
        </div>
        {% empty %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-bell-slash"></i>
            </div>
            <p>You don't have any reminders set up yet.</p>
            <p>Create a reminder to help maintain your journaling habit!</p>
            <a href="{% url 'reminder_create' %}" class="button primary-btn">
                <i class="fas fa-plus animated-icon"></i> Create First Reminder
            </a>
        </div>
        {% endfor %}
    </div>
</div>

<script>
    function toggleReminder(reminderId, isActive) {
        // Send AJAX request to update reminder status
        fetch(`/reminders/${reminderId}/toggle/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({ active: isActive })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const reminderCard = document.querySelector(`.reminder-card[data-id="${reminderId}"]`);
                if (isActive) {
                    reminderCard.classList.remove('inactive');
                } else {
                    reminderCard.classList.add('inactive');
                }
            }
        });
    }
</script>
{% endblock %}

