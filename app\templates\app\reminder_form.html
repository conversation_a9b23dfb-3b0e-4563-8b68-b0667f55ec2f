{% extends 'app/base.html' %}

{% block content %}
<div class="form-container">
    <h2>{% if form.instance.pk %}Edit Reminder{% else %}New Reminder{% endif %}</h2>
    
    <form method="post" id="reminderForm">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="id_title">Reminder Title:</label>
            {{ form.title }}
            {% if form.title.errors %}
                <div class="error-message">{{ form.title.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_frequency">Frequency:</label>
            {{ form.frequency }}
            {% if form.frequency.errors %}
                <div class="error-message">{{ form.frequency.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_time">Time:</label>
            {{ form.time }}
            <div class="help-text">{{ form.time.help_text }}</div>
            {% if form.time.errors %}
                <div class="error-message">{{ form.time.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group days-selection" id="daysGroup">
            <label>Days of the Week:</label>
            <div class="days-checkboxes">
                {{ form.days }}
            </div>
            <div class="help-text">{{ form.days.help_text }}</div>
            {% if form.days.errors %}
                <div class="error-message">{{ form.days.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="id_active" class="checkbox-label">
                {{ form.active }}
                Active
            </label>
            {% if form.active.errors %}
                <div class="error-message">{{ form.active.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-actions">
            <button type="submit" class="primary-btn">
                <i class="fas fa-save animated-icon"></i> Save Reminder
            </button>
            <a href="{% url 'reminder_list' %}" class="button secondary">
                <i class="fas fa-times-circle animated-icon"></i> Cancel
            </a>
        </div>
    </form>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const frequencySelect = document.getElementById('id_frequency');
        const daysGroup = document.getElementById('daysGroup');
        
        // Function to toggle days visibility based on frequency
        function toggleDaysVisibility() {
            if (frequencySelect.value === 'daily') {
                daysGroup.style.display = 'none';
            } else {
                daysGroup.style.display = 'block';
            }
        }
        
        // Initial state
        toggleDaysVisibility();
        
        // Listen for changes
        frequencySelect.addEventListener('change', toggleDaysVisibility);
    });
</script>
{% endblock %}