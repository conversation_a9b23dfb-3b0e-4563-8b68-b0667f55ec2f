/* Calendar Specific Styles */
:root {
    --calendar-primary: #667eea;
    --calendar-secondary: #764ba2;
    --calendar-accent: #f093fb;
    --calendar-success: #4facfe;
    --calendar-warning: #f6d365;
    --calendar-danger: #ff6b6b;
    --calendar-light: #f8f9fa;
    --calendar-dark: #343a40;
}

/* Calendar Container */
.calendar-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.calendar-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

/* Calendar Header */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, var(--calendar-primary), var(--calendar-secondary));
    border-radius: 15px;
    color: white;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.nav-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.calendar-title {
    font-size: 2.2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.calendar-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.calendar-actions .button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
}

.calendar-actions .button:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

/* Calendar Grid */
.calendar-grid {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: linear-gradient(135deg, var(--calendar-primary), var(--calendar-secondary));
    color: white;
}

.weekday {
    padding: 20px 15px;
    text-align: center;
    font-weight: 700;
    font-size: 1rem;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: #e9ecef;
    padding: 2px;
}

.calendar-day {
    background: white;
    min-height: 140px;
    padding: 12px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
}

.calendar-day:hover {
    background: #f8f9fa;
    transform: scale(1.02);
    z-index: 2;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.calendar-day.empty {
    background: #f1f3f4;
    cursor: default;
    opacity: 0.5;
}

.calendar-day.today {
    background: linear-gradient(135deg, var(--calendar-primary), var(--calendar-secondary));
    color: white;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4);
}

.calendar-day.today .day-number {
    color: white;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.calendar-day.has-entries {
    border-left: 5px solid var(--calendar-accent);
    background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
}

.day-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--calendar-dark);
    margin-bottom: 8px;
    text-align: center;
}

.day-entries {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
    overflow: hidden;
}

.entry-indicator {
    font-size: 0.75rem;
    color: var(--calendar-accent);
    opacity: 0.9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 6px;
    background: rgba(240, 147, 251, 0.1);
    border-radius: 10px;
    border-left: 3px solid var(--calendar-accent);
}

.more-entries {
    font-size: 0.7rem;
    color: var(--calendar-dark);
    opacity: 0.6;
    font-style: italic;
    text-align: center;
    margin-top: 2px;
}

.day-actions {
    display: flex;
    gap: 5px;
    margin-top: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
    justify-content: center;
}

.calendar-day:hover .day-actions {
    opacity: 1;
}

.view-day, .add-entry {
    background: var(--calendar-primary);
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-day:hover {
    background: var(--calendar-success);
    transform: scale(1.1);
}

.add-entry:hover {
    background: var(--calendar-accent);
    transform: scale(1.1);
}

/* Calendar Legend */
.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: var(--calendar-dark);
    font-weight: 500;
}

.legend-color {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.today-color {
    background: linear-gradient(135deg, var(--calendar-primary), var(--calendar-secondary));
}

.has-entries-color {
    background: var(--calendar-accent);
}

.entry-indicator-sample {
    color: var(--calendar-accent);
    font-size: 1rem;
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 3% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 700px;
    box-shadow: 0 25px 80px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, var(--calendar-primary), var(--calendar-secondary));
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-body {
    padding: 25px;
    max-height: 500px;
    overflow-y: auto;
}

.close-modal {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;
}

.close-modal:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .calendar-wrapper {
        margin: 10px;
        padding: 15px;
    }
    
    .calendar-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .calendar-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .calendar-day {
        min-height: 100px;
        padding: 8px;
    }
    
    .day-number {
        font-size: 1rem;
    }
    
    .calendar-legend {
        gap: 15px;
        flex-direction: column;
        align-items: center;
    }
    
    .legend-item {
        font-size: 0.8rem;
    }
    
    .weekday {
        padding: 15px 10px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .calendar-header {
        padding: 15px;
    }
    
    .calendar-title {
        font-size: 1.4rem;
        flex-direction: column;
        gap: 5px;
    }
    
    .calendar-navigation {
        gap: 15px;
    }
    
    .nav-btn {
        padding: 10px 12px;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 6px;
    }
    
    .day-number {
        font-size: 0.9rem;
    }
    
    .entry-indicator {
        font-size: 0.7rem;
        padding: 1px 4px;
    }
}
