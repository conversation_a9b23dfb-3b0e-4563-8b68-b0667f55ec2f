/* Cyberpunk Calendar Theme */
:root {
    /* Calendar Colors - Matching Main Theme */
    --cal-bg-primary: #0a0a0f;
    --cal-bg-secondary: #1a1a2e;
    --cal-bg-card: #0f1419;
    --cal-neon-cyan: #00ffff;
    --cal-neon-purple: #bf00ff;
    --cal-neon-pink: #ff0080;
    --cal-neon-green: #39ff14;
    --cal-text-primary: #ffffff;
    --cal-text-secondary: #b8b8b8;
    --cal-gradient-neon: linear-gradient(135deg, #00ffff 0%, #bf00ff 50%, #ff0080 100%);
    --cal-shadow-neon: 0 0 20px rgba(0, 255, 255, 0.5);
    --cal-shadow-purple: 0 0 20px rgba(191, 0, 255, 0.5);
    --cal-shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.5);
    --cal-border-neon: 1px solid rgba(0, 255, 255, 0.3);
    --cal-border-purple: 1px solid rgba(191, 0, 255, 0.3);
}

/* Calendar Container */
.calendar-container {
    background: var(--cal-bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(191, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 0, 128, 0.05) 0%, transparent 50%);
    min-height: 100vh;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
}

.calendar-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0,255,255,0.02) 50%, transparent 70%);
    animation: backgroundShimmer 10s infinite;
    pointer-events: none;
}

@keyframes backgroundShimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.calendar-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--cal-bg-card);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(191,0,255,0.05) 100%);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: var(--cal-shadow-dark);
    border: var(--cal-border-neon);
    position: relative;
    z-index: 1;
}

/* Calendar Header */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;
    background: var(--cal-bg-secondary);
    background-image: var(--cal-gradient-neon);
    border-radius: 20px;
    color: var(--cal-text-primary);
    box-shadow: var(--cal-shadow-neon);
    position: relative;
    overflow: hidden;
    border: var(--cal-border-neon);
}

.calendar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 2;
    position: relative;
}

.nav-btn {
    background: rgba(255,255,255,0.15);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    font-size: 1.1rem;
    min-width: 48px;
    min-height: 48px;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.nav-btn:active {
    transform: translateY(0) scale(0.98);
}

.calendar-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-shadow: 0 0 20px var(--cal-neon-cyan);
    z-index: 2;
    position: relative;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-family: 'Orbitron', monospace;
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 20px var(--cal-neon-cyan), 0 0 30px var(--cal-neon-cyan); }
    to { text-shadow: 0 0 30px var(--cal-neon-cyan), 0 0 40px var(--cal-neon-cyan), 0 0 50px var(--cal-neon-cyan); }
}

.calendar-title i {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px var(--cal-neon-cyan));
    animation: iconPulse 1.5s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.calendar-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    z-index: 2;
    position: relative;
}

.calendar-actions .button {
    background: rgba(255,255,255,0.15);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calendar-actions .button:hover {
    background: rgba(255,255,255,0.25);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.calendar-actions .primary-btn {
    background: var(--cal-accent);
    border-color: var(--cal-accent);
}

.calendar-actions .primary-btn:hover {
    background: #0891b2;
    border-color: #0891b2;
}

/* Calendar Grid */
.calendar-grid {
    background: var(--cal-bg-card);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--cal-shadow-dark);
    margin-bottom: 2rem;
    border: var(--cal-border-neon);
    position: relative;
}

.calendar-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0,255,255,0.02) 50%, transparent 70%);
    animation: gridShimmer 8s infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes gridShimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%); }
    50% { transform: translateX(100%) translateY(100%); }
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--cal-bg-secondary);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(191,0,255,0.1) 100%);
    color: var(--cal-text-primary);
    position: relative;
    z-index: 2;
}

.calendar-weekdays::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
}

.weekday {
    padding: 1.25rem 1rem;
    text-align: center;
    font-weight: 700;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    position: relative;
}

.weekday::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: rgba(255,255,255,0.1);
}

.weekday:last-child::after {
    display: none;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    background: var(--cal-bg-secondary);
    padding: 2px;
    z-index: 2;
    position: relative;
}

.calendar-day {
    background: var(--cal-bg-card);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.02) 0%, rgba(191,0,255,0.02) 100%);
    min-height: 160px;
    padding: 1rem;
    position: relative;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.calendar-day::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(99, 102, 241, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.calendar-day:hover::before {
    opacity: 1;
}

.calendar-day:hover {
    transform: translateY(-6px);
    z-index: 10;
    box-shadow: var(--cal-shadow-neon);
    border-color: var(--cal-neon-cyan);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(191,0,255,0.1) 100%);
}

.calendar-day.empty {
    background: rgba(0, 0, 0, 0.2);
    cursor: default;
    opacity: 0.3;
    border-color: rgba(255, 255, 255, 0.05);
}

.calendar-day.empty:hover {
    transform: none;
    box-shadow: none;
    background: rgba(0, 0, 0, 0.2);
}

.calendar-day.today {
    background: var(--cal-gradient-neon);
    color: var(--cal-text-primary);
    box-shadow: var(--cal-shadow-neon);
    border: 2px solid var(--cal-neon-cyan);
    position: relative;
    animation: todayPulse 2s ease-in-out infinite;
}

@keyframes todayPulse {
    0%, 100% {
        box-shadow: var(--cal-shadow-neon);
        border-color: var(--cal-neon-cyan);
    }
    50% {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
        border-color: var(--cal-neon-purple);
    }
}

.calendar-day.today::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
    animation: todayShimmer 3s infinite;
}

@keyframes todayShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.calendar-day.today .day-number {
    color: var(--cal-text-primary);
    font-weight: 900;
    text-shadow: 0 0 10px var(--cal-text-primary);
    z-index: 2;
    position: relative;
    font-family: 'Orbitron', monospace;
}

.calendar-day.has-entries {
    border-left: 4px solid var(--cal-neon-purple);
    background-image: linear-gradient(135deg, rgba(191,0,255,0.05) 0%, rgba(0,255,255,0.05) 100%);
    position: relative;
}

.calendar-day.has-entries::before {
    background: linear-gradient(135deg, rgba(191, 0, 255, 0.1) 0%, rgba(0, 255, 255, 0.1) 100%);
}

.day-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--cal-text-primary);
    margin-bottom: 0.75rem;
    text-align: left;
    z-index: 2;
    position: relative;
    line-height: 1;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 5px var(--cal-neon-cyan);
}

.day-entries {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    overflow: hidden;
    z-index: 2;
    position: relative;
}

.entry-indicator {
    font-size: 0.75rem;
    color: var(--cal-neon-purple);
    background: rgba(191, 0, 255, 0.1);
    border: 1px solid rgba(191, 0, 255, 0.3);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    backdrop-filter: blur(5px);
    text-shadow: 0 0 5px var(--cal-neon-purple);
}

.entry-indicator:hover {
    background: rgba(191, 0, 255, 0.2);
    border-color: var(--cal-neon-purple);
    transform: translateX(3px) scale(1.02);
    box-shadow: 0 0 10px rgba(191, 0, 255, 0.3);
}

.entry-indicator i {
    font-size: 0.625rem;
    filter: drop-shadow(0 0 3px currentColor);
}

.more-entries {
    font-size: 0.75rem;
    color: var(--cal-gray);
    font-weight: 600;
    text-align: center;
    margin-top: 0.25rem;
    padding: 0.25rem;
    background: rgba(100, 116, 139, 0.1);
    border-radius: 6px;
    z-index: 2;
    position: relative;
}

.day-actions {
    display: flex;
    gap: 0.375rem;
    margin-top: auto;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    justify-content: center;
    z-index: 3;
    position: relative;
    padding-top: 0.5rem;
}

.calendar-day:hover .day-actions {
    opacity: 1;
    transform: translateY(-2px);
}

.view-day, .add-entry {
    background: linear-gradient(135deg, var(--cal-neon-cyan) 0%, var(--cal-neon-purple) 100%);
    color: var(--cal-text-primary);
    border: var(--cal-border-neon);
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 32px;
    box-shadow: var(--cal-shadow-neon);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Orbitron', monospace;
}

.view-day:hover {
    background: linear-gradient(135deg, var(--cal-neon-green) 0%, var(--cal-neon-cyan) 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 0 15px rgba(57, 255, 20, 0.5);
    text-shadow: 0 0 5px var(--cal-text-primary);
}

.add-entry:hover {
    background: linear-gradient(135deg, var(--cal-neon-pink) 0%, var(--cal-neon-purple) 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 0 15px rgba(255, 0, 128, 0.5);
    text-shadow: 0 0 5px var(--cal-text-primary);
}

/* Calendar Legend */
.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: var(--cal-shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: var(--cal-dark);
    font-weight: 600;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.legend-item:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.legend-color::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: legendShimmer 3s infinite;
}

@keyframes legendShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.today-color {
    background: linear-gradient(135deg, var(--cal-primary), var(--cal-secondary));
}

.has-entries-color {
    background: linear-gradient(135deg, var(--cal-accent), #0891b2);
}

.entry-indicator-sample {
    color: var(--cal-primary);
    font-size: 1rem;
    filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3));
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    backdrop-filter: blur(8px);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    margin: 3% auto;
    padding: 0;
    border-radius: 24px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 32px 64px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-32px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, var(--cal-primary) 0%, var(--cal-secondary) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: modalHeaderShimmer 3s infinite;
}

@keyframes modalHeaderShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.modal-header h3 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    z-index: 2;
    position: relative;
}

.modal-body {
    padding: 2rem;
    max-height: 600px;
    overflow-y: auto;
    background: #fafafa;
}

.close-modal {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: rgba(255,255,255,0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.5rem;
    border-radius: 12px;
    z-index: 3;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.close-modal:hover {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg) scale(1.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .calendar-wrapper {
        margin: 1rem;
        padding: 1.5rem;
    }

    .calendar-header {
        padding: 1.25rem 1.5rem;
    }

    .calendar-title {
        font-size: 2.25rem;
    }
}

@media (max-width: 1024px) {
    .calendar-container {
        padding: 1.5rem 0.75rem;
    }

    .calendar-wrapper {
        padding: 1.25rem;
    }

    .calendar-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 1.5rem;
    }

    .calendar-title {
        font-size: 2rem;
    }

    .calendar-navigation {
        order: 2;
    }

    .calendar-actions {
        order: 3;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .calendar-container {
        padding: 1rem 0.5rem;
    }

    .calendar-wrapper {
        padding: 1rem;
        border-radius: 20px;
    }

    .calendar-header {
        padding: 1.25rem;
        border-radius: 16px;
    }

    .calendar-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .calendar-day {
        min-height: 120px;
        padding: 0.75rem;
    }

    .day-number {
        font-size: 1.125rem;
        text-align: center;
        margin-bottom: 0.5rem;
    }

    .calendar-legend {
        gap: 1rem;
        padding: 1.25rem;
        flex-direction: column;
        align-items: center;
    }

    .legend-item {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .weekday {
        padding: 1rem 0.75rem;
        font-size: 0.8rem;
    }

    .nav-btn {
        min-width: 44px;
        min-height: 44px;
        padding: 0.625rem 0.875rem;
    }

    .calendar-actions .button {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .calendar-header {
        padding: 1rem;
    }

    .calendar-title {
        font-size: 1.5rem;
    }

    .calendar-navigation {
        gap: 1rem;
    }

    .nav-btn {
        min-width: 40px;
        min-height: 40px;
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
    }

    .calendar-day {
        min-height: 100px;
        padding: 0.5rem;
    }

    .day-number {
        font-size: 1rem;
        margin-bottom: 0.375rem;
    }

    .entry-indicator {
        font-size: 0.7rem;
        padding: 0.1875rem 0.375rem;
        gap: 0.1875rem;
    }

    .day-actions {
        gap: 0.25rem;
        padding-top: 0.375rem;
    }

    .view-day, .add-entry {
        padding: 0.375rem 0.5rem;
        font-size: 0.7rem;
        min-width: 32px;
        min-height: 28px;
    }

    .calendar-legend {
        padding: 1rem;
        gap: 0.75rem;
    }

    .legend-item {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        gap: 0.5rem;
    }

    .legend-color {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 480px) {
    .calendar-container {
        padding: 0.75rem 0.25rem;
    }

    .calendar-wrapper {
        padding: 0.75rem;
        border-radius: 16px;
    }

    .calendar-header {
        padding: 0.875rem;
        margin-bottom: 1.5rem;
    }

    .calendar-title {
        font-size: 1.375rem;
    }

    .calendar-day {
        min-height: 90px;
        padding: 0.375rem;
    }

    .day-number {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .weekday {
        padding: 0.875rem 0.5rem;
        font-size: 0.75rem;
    }

    .entry-indicator {
        font-size: 0.65rem;
        padding: 0.125rem 0.25rem;
    }

    .more-entries {
        font-size: 0.65rem;
        padding: 0.1875rem;
    }

    .day-actions {
        opacity: 1; /* Always show on mobile */
        transform: none;
    }

    .view-day, .add-entry {
        padding: 0.25rem 0.375rem;
        font-size: 0.65rem;
        min-width: 28px;
        min-height: 24px;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-header {
        padding: 1.25rem;
    }

    .modal-header h3 {
        font-size: 1.375rem;
    }

    .modal-body {
        padding: 1.25rem;
    }
}
