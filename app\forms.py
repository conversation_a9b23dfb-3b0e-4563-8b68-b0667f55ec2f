from django import forms
from django.contrib.auth.models import User
from .models import JournalEntry, JournalReminder

class JournalEntryForm(forms.ModelForm):
    class Meta:
        model = JournalEntry
        fields = ['title', 'content', 'image', 'category', 'tags']
        widgets = {
            'content': forms.Textarea(attrs={'rows': 15, 'class': 'journal-content'}),
            'tags': forms.TextInput(attrs={'placeholder': 'Enter tags separated by commas'}),
        }

class JournalReminderForm(forms.ModelForm):
    class Meta:
        model = JournalReminder
        fields = ['title', 'frequency', 'time', 'days', 'active']
        widgets = {
            'time': forms.TimeInput(attrs={'type': 'time'}),
            'days': forms.TextInput(attrs={'placeholder': 'e.g., Mon,Wed,Fri'}),
        }

class UserProfileForm(forms.ModelForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name']




