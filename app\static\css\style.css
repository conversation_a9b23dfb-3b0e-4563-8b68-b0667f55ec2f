/* Cyberpunk Neon Theme - Journal App */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

:root {
    /* Primary Dark Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: #0f1419;

    /* Neon Colors */
    --neon-cyan: #00ffff;
    --neon-purple: #bf00ff;
    --neon-pink: #ff0080;
    --neon-green: #39ff14;
    --neon-orange: #ff6600;
    --neon-blue: #0080ff;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #666666;
    --text-accent: #00ffff;

    /* Gradients */
    --gradient-neon: linear-gradient(135deg, #00ffff 0%, #bf00ff 50%, #ff0080 100%);
    --gradient-dark: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 100%);
    --gradient-card: linear-gradient(135deg, rgba(0,255,255,0.1) 0%, rgba(191,0,255,0.1) 100%);
    --gradient-button: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);

    /* Effects */
    --shadow-neon: 0 0 20px rgba(0, 255, 255, 0.5);
    --shadow-purple: 0 0 20px rgba(191, 0, 255, 0.5);
    --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.5);
    --border-neon: 1px solid rgba(0, 255, 255, 0.3);
    --border-purple: 1px solid rgba(191, 0, 255, 0.3);

    /* Animation */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --glow-animation: glow 2s ease-in-out infinite alternate;
}

/* Global Reset & Animations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@keyframes glow {
    from { text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor; }
    to { text-shadow: 0 0 20px currentColor, 0 0 30px currentColor, 0 0 40px currentColor; }
}

@keyframes neonPulse {
    0%, 100% { box-shadow: 0 0 5px var(--neon-cyan), 0 0 10px var(--neon-cyan), 0 0 15px var(--neon-cyan); }
    50% { box-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-cyan); }
}

@keyframes slideInFromLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Base Styles */
body {
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(191, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 0, 128, 0.05) 0%, transparent 50%);
    color: var(--text-primary);
    font-family: 'Rajdhani', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1.5rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 2px;
}

h1 {
    font-size: 3rem;
    background: var(--gradient-neon);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: var(--glow-animation);
}

h2 {
    font-size: 2.5rem;
    color: var(--neon-cyan);
    text-shadow: 0 0 10px var(--neon-cyan);
}

h3 {
    font-size: 2rem;
    color: var(--neon-purple);
}

p {
    margin-bottom: 1rem;
    font-weight: 400;
    color: var(--text-secondary);
}

a {
    color: var(--neon-cyan);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

a:hover {
    color: var(--neon-purple);
    text-shadow: 0 0 10px currentColor;
}

a::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-neon);
    transition: width 0.3s ease;
}

a:hover::before {
    width: 100%;
}

/* Header */
header {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(191,0,255,0.05) 100%);
    padding: 1.5rem 0;
    box-shadow: var(--shadow-dark);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: var(--border-neon);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideInFromLeft 0.8s ease-out;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo h1 {
    font-size: 2rem;
    margin: 0;
    background: var(--gradient-neon);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px var(--neon-cyan);
    animation: var(--glow-animation);
}

.logo svg {
    filter: drop-shadow(0 0 10px var(--neon-cyan));
}

nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

nav a {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    border: var(--border-neon);
    background: rgba(0, 255, 255, 0.05);
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
}

nav a:hover {
    background: rgba(0, 255, 255, 0.1);
    box-shadow: var(--shadow-neon);
    transform: translateY(-2px);
    color: var(--neon-cyan);
}

nav a::before {
    display: none;
}

nav a i {
    font-size: 1.2rem;
    filter: drop-shadow(0 0 5px currentColor);
}

/* Main Content */
main {
    padding: 3rem 0;
    min-height: calc(100vh - 200px);
    animation: fadeInUp 1s ease-out;
}

/* Footer */
footer {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg, rgba(191,0,255,0.05) 0%, rgba(0,255,255,0.05) 100%);
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: var(--border-purple);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1.5rem;
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.social-links {
    display: flex;
    gap: 2rem;
}

.social-links a {
    color: var(--text-secondary);
    font-size: 1.5rem;
    padding: 1rem;
    border-radius: 50%;
    border: var(--border-neon);
    background: rgba(0, 255, 255, 0.05);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
}

.social-links a:hover {
    color: var(--neon-cyan);
    background: rgba(0, 255, 255, 0.1);
    box-shadow: var(--shadow-neon);
    transform: translateY(-3px) scale(1.1);
}

.social-links a::before {
    display: none;
}

/* Cards & Content */
.card, .journal-detail, .entry-card, .auth-container {
    background: var(--bg-card);
    background-image: var(--gradient-card);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-dark);
    border: var(--border-neon);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0,255,255,0.05) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover::before, .journal-detail:hover::before, .entry-card:hover::before {
    opacity: 1;
}

.card:hover, .journal-detail:hover, .entry-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-neon);
}

.entry-card {
    display: flex;
    flex-direction: column;
    animation: fadeInUp 0.6s ease-out;
}

.entry-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: 15px;
    margin-bottom: 1.5rem;
    cursor: pointer;
    border: var(--border-neon);
    position: relative;
}

.entry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
    filter: brightness(0.8) contrast(1.2);
}

.entry-image:hover img {
    transform: scale(1.1);
    filter: brightness(1) contrast(1.3);
}

.entry-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--neon-cyan);
    text-shadow: 0 0 10px var(--neon-cyan);
}

.entry-date {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.entry-date i {
    color: var(--neon-purple);
    filter: drop-shadow(0 0 5px var(--neon-purple));
}

.entry-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 5px var(--neon-cyan);
}

input, textarea, select {
    padding: 1rem 1.5rem;
    border: var(--border-neon);
    border-radius: 15px;
    background: var(--bg-card);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(191,0,255,0.05) 100%);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Rajdhani', sans-serif;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--neon-cyan);
    box-shadow: var(--shadow-neon);
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

textarea {
    resize: vertical;
    min-height: 150px;
    font-family: 'Rajdhani', sans-serif;
}

/* Buttons */
button, .button, input[type="submit"] {
    padding: 1rem 2rem;
    border-radius: 25px;
    border: var(--border-neon);
    background: var(--gradient-button);
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    font-family: 'Orbitron', monospace;
}

button::before, .button::before, input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
    left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
    background: linear-gradient(135deg, var(--neon-purple) 0%, var(--neon-pink) 100%);
    box-shadow: var(--shadow-purple);
    transform: translateY(-3px) scale(1.05);
    text-shadow: 0 0 10px var(--text-primary);
}

.button.small {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

.button.secondary {
    background: transparent;
    border: var(--border-purple);
    color: var(--neon-purple);
}

.button.secondary:hover {
    background: rgba(191, 0, 255, 0.1);
    color: var(--text-primary);
}

.button.danger {
    background: linear-gradient(135deg, var(--neon-orange) 0%, #ff0000 100%);
    border-color: var(--neon-orange);
}

.button.danger:hover {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
}

.primary-btn {
    background: var(--gradient-neon);
    animation: neonPulse 2s infinite;
}

.pulse-button {
    animation: neonPulse 1.5s infinite;
}

/* Utilities */
.page-title {
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.error-message {
  color: var(--error-color);
  margin-bottom: 1rem;
}

.success-message {
  color: var(--success-color);
  margin-bottom: 1rem;
}

/* Animations */
.animated-icon {
  transition: transform 0.3s ease;
}

a:hover .animated-icon, button:hover .animated-icon {
  transform: scale(1.2);
}

/* Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  overflow: auto;
}

.image-modal.active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 35px;
  color: var(--text-color);
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
}

/* Particles */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: var(--accent-color);
  opacity: 0.3;
  border-radius: 50%;
  animation: float 20s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20vh) translateX(20vw);
  }
  50% {
    transform: translateY(-40vh) translateX(0);
  }
  75% {
    transform: translateY(-20vh) translateX(-20vw);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  nav {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .entry-list {
    grid-template-columns: 1fr;
  }
}

/* Remove theme switcher */
.theme-switcher {
  display: none;
}




