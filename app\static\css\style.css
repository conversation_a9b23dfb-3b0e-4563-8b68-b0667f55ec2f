/* Enhanced Modern Theme with Dark/Light Mode */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

:root {
    /* Core Colors */
    --primary-hue: 220;
    --secondary-hue: 280;
    --accent-hue: 200;
    --success-hue: 140;
    --warning-hue: 45;
    --danger-hue: 0;

    /* Light Theme */
    --bg-primary-light: hsl(var(--primary-hue), 15%, 98%);
    --bg-secondary-light: hsl(var(--primary-hue), 20%, 95%);
    --bg-tertiary-light: hsl(var(--primary-hue), 25%, 92%);
    --bg-card-light: hsl(var(--primary-hue), 30%, 99%);
    --text-primary-light: hsl(var(--primary-hue), 30%, 15%);
    --text-secondary-light: hsl(var(--primary-hue), 20%, 35%);
    --text-muted-light: hsl(var(--primary-hue), 15%, 55%);
    --border-light: hsl(var(--primary-hue), 20%, 85%);
    --shadow-light: 0 4px 20px hsla(var(--primary-hue), 30%, 0%, 0.1);

    /* Dark Theme */
    --bg-primary-dark: hsl(var(--primary-hue), 25%, 8%);
    --bg-secondary-dark: hsl(var(--primary-hue), 30%, 12%);
    --bg-tertiary-dark: hsl(var(--primary-hue), 35%, 16%);
    --bg-card-dark: hsl(var(--primary-hue), 40%, 10%);
    --text-primary-dark: hsl(var(--primary-hue), 20%, 95%);
    --text-secondary-dark: hsl(var(--primary-hue), 15%, 75%);
    --text-muted-dark: hsl(var(--primary-hue), 10%, 55%);
    --border-dark: hsl(var(--primary-hue), 25%, 25%);
    --shadow-dark: 0 8px 32px hsla(var(--primary-hue), 50%, 0%, 0.3);

    /* Accent Colors */
    --primary-color: hsl(var(--primary-hue), 85%, 60%);
    --secondary-color: hsl(var(--secondary-hue), 75%, 65%);
    --accent-color: hsl(var(--accent-hue), 80%, 55%);
    --success-color: hsl(var(--success-hue), 70%, 50%);
    --warning-color: hsl(var(--warning-hue), 85%, 60%);
    --danger-color: hsl(var(--danger-hue), 75%, 60%);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg,
        hsl(var(--primary-hue), 85%, 60%) 0%,
        hsl(var(--secondary-hue), 75%, 65%) 100%);
    --gradient-accent: linear-gradient(135deg,
        hsl(var(--accent-hue), 80%, 55%) 0%,
        hsl(var(--primary-hue), 85%, 60%) 100%);
    --gradient-success: linear-gradient(135deg,
        hsl(var(--success-hue), 70%, 50%) 0%,
        hsl(var(--accent-hue), 80%, 55%) 100%);

    /* Animation & Transitions */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Enhanced Spacing Scale */
    --space-xs: 0.375rem;
    --space-sm: 0.75rem;
    --space-md: 1.25rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;
    --space-4xl: 8rem;
    --space-5xl: 12rem;

    /* Content Spacing */
    --content-spacing: var(--space-xl);
    --section-spacing: var(--space-3xl);
    --component-spacing: var(--space-lg);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* Theme Variables - Default to Dark */
:root {
    --bg-primary: var(--bg-primary-dark);
    --bg-secondary: var(--bg-secondary-dark);
    --bg-tertiary: var(--bg-tertiary-dark);
    --bg-card: var(--bg-card-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-muted: var(--text-muted-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-dark);
}

/* Light Theme */
[data-theme="light"] {
    --bg-primary: var(--bg-primary-light);
    --bg-secondary: var(--bg-secondary-light);
    --bg-tertiary: var(--bg-tertiary-light);
    --bg-card: var(--bg-card-light);
    --text-primary: var(--text-primary-light);
    --text-secondary: var(--text-secondary-light);
    --text-muted: var(--text-muted-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-light);
}

/* Global Reset & Animations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, hsla(var(--primary-hue), 70%, 60%, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsla(var(--secondary-hue), 70%, 60%, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, hsla(var(--accent-hue), 70%, 60%, 0.02) 0%, transparent 50%);
    color: var(--text-primary);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    transition: var(--transition-smooth);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--content-spacing);
}

.container-narrow {
    max-width: 800px;
}

.container-wide {
    max-width: 1600px;
}

.container-fluid {
    max-width: none;
    padding: 0 var(--space-md);
}

/* Utility Classes */
.fade-in { animation: fadeIn 0.6s ease-out; }
.slide-up { animation: slideUp 0.6s ease-out; }
.slide-down { animation: slideDown 0.6s ease-out; }
.slide-left { animation: slideLeft 0.6s ease-out; }
.slide-right { animation: slideRight 0.6s ease-out; }
.pulse { animation: pulse 2s infinite; }
.glow { animation: glow 2s infinite; }
.float { animation: float 3s ease-in-out infinite; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--space-lg);
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-xl);
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--primary-color);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2.25rem);
    font-weight: 600;
    color: var(--secondary-color);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.875rem);
    font-weight: 600;
}

h5 {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    font-weight: 600;
}

h6 {
    font-size: clamp(1rem, 1.5vw, 1.25rem);
    font-weight: 600;
}

p {
    margin-bottom: var(--space-md);
    font-weight: 400;
    color: var(--text-secondary);
    line-height: 1.7;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--text-secondary);
    line-height: 1.6;
}

.small {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.code {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.875rem;
    background: var(--bg-tertiary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-smooth);
    position: relative;
    font-weight: 500;
}

a:hover {
    color: var(--accent-color);
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

.link-underline {
    position: relative;
}

.link-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: width 0.3s ease;
}

.link-underline:hover::after {
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    padding: 2px;
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px hsla(var(--primary-hue), 85%, 60%, 0.1);
}

.theme-toggle-slider {
    width: 22px;
    height: 22px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-color);
}

.theme-toggle-slider i {
    font-size: 12px;
    color: white;
}

[data-theme="light"] .theme-toggle-slider {
    transform: translateX(30px);
}

/* Header */
header {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg,
        hsla(var(--primary-hue), 70%, 60%, 0.05) 0%,
        hsla(var(--secondary-hue), 70%, 60%, 0.05) 100%);
    padding: var(--space-lg) 0;
    box-shadow: var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
    transition: var(--transition-smooth);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideDown 0.8s ease-out;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo h1 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -0.02em;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-accent);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    box-shadow: var(--shadow-color);
    transition: var(--transition-smooth);
}

.logo-icon:hover {
    transform: rotate(5deg) scale(1.05);
}

.nav-container {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

nav {
    display: flex;
    gap: var(--space-lg);
    align-items: center;
}

nav a {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    border: 1px solid transparent;
    background: transparent;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    transition: left 0.3s ease;
    z-index: -1;
}

nav a:hover::before {
    left: 0;
}

nav a:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-color);
}

nav a i {
    font-size: 1rem;
    transition: var(--transition-smooth);
}

nav a:hover i {
    transform: scale(1.1);
}

/* Main Content */
main {
    padding: var(--space-3xl) 0;
    min-height: calc(100vh - 200px);
    animation: fadeIn 1s ease-out;
}

.page-title {
    text-align: center;
    margin-bottom: var(--space-3xl);
    animation: slideUp 0.8s ease-out;
}

.page-subtitle {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.25rem;
    margin-bottom: var(--space-2xl);
    animation: slideUp 0.8s ease-out 0.2s both;
}

/* Cards & Content */
.card, .journal-detail, .entry-card, .auth-container {
    background: var(--bg-card);
    background-image: linear-gradient(135deg,
        hsla(var(--primary-hue), 70%, 60%, 0.02) 0%,
        hsla(var(--secondary-hue), 70%, 60%, 0.02) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-color);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, hsla(var(--primary-hue), 70%, 60%, 0.02) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover::before, .journal-detail:hover::before, .entry-card:hover::before {
    opacity: 1;
}

.card:hover, .journal-detail:hover, .entry-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px hsla(var(--primary-hue), 50%, 0%, 0.15);
    border-color: var(--primary-color);
}

.entry-card {
    display: flex;
    flex-direction: column;
    animation: slideUp 0.6s ease-out;
}

.entry-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-lg);
    cursor: pointer;
    border: 2px solid var(--border-color);
    position: relative;
    transition: var(--transition-smooth);
}

.entry-image:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px hsla(var(--primary-hue), 85%, 60%, 0.1);
}

.entry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.entry-image:hover img {
    transform: scale(1.05);
}

.entry-title {
    font-size: 1.5rem;
    margin-bottom: var(--space-md);
    color: var(--primary-color);
    font-weight: 600;
}

.entry-date {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
}

.entry-date i {
    color: var(--accent-color);
    font-size: 1rem;
}

.entry-actions {
    display: flex;
    gap: var(--space-md);
    margin-top: auto;
    flex-wrap: wrap;
}

/* Footer */
footer {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg,
        hsla(var(--secondary-hue), 70%, 60%, 0.05) 0%,
        hsla(var(--primary-hue), 70%, 60%, 0.05) 100%);
    padding: var(--space-2xl) 0;
    margin-top: var(--space-3xl);
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--space-lg);
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: var(--space-lg);
}

.social-links a {
    color: var(--text-secondary);
    font-size: 1.25rem;
    padding: var(--space-md);
    border-radius: 50%;
    border: 2px solid var(--border-color);
    background: var(--bg-tertiary);
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
}

.social-links a:hover {
    color: var(--primary-color);
    background: var(--bg-card);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-color);
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
    margin-bottom: var(--space-xs);
}

input, textarea, select {
    padding: var(--space-md) var(--space-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: var(--transition-smooth);
    backdrop-filter: blur(10px);
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px hsla(var(--primary-hue), 85%, 60%, 0.1);
    background: var(--bg-card);
    transform: translateY(-2px);
}

textarea {
    resize: vertical;
    min-height: 120px;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

/* Buttons */
button, .button, input[type="submit"] {
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-color);
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    font-family: 'Inter', sans-serif;
    min-height: 44px;
}

button::before, .button::before, input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
    left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
    background: var(--gradient-accent);
    border-color: var(--accent-color);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 25px hsla(var(--primary-hue), 85%, 60%, 0.3);
}

button:active, .button:active, input[type="submit"]:active {
    transform: translateY(-1px) scale(0.98);
}

button:focus, .button:focus, input[type="submit"]:focus {
    outline: none;
    box-shadow: 0 0 0 4px hsla(var(--primary-hue), 85%, 60%, 0.2);
}

.button.small {
    padding: var(--space-sm) var(--space-lg);
    font-size: 0.875rem;
    min-height: 36px;
}

.button.large {
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1.125rem;
    min-height: 52px;
}

.button.secondary {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.button.secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.button.success {
    background: var(--gradient-success);
    border-color: var(--success-color);
}

.button.success:hover {
    background: linear-gradient(135deg,
        hsl(var(--success-hue), 70%, 45%) 0%,
        hsl(var(--accent-hue), 80%, 50%) 100%);
}

.button.warning {
    background: linear-gradient(135deg,
        hsl(var(--warning-hue), 85%, 60%) 0%,
        hsl(var(--warning-hue), 85%, 50%) 100%);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.button.danger {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 60%) 0%,
        hsl(var(--danger-hue), 75%, 50%) 100%);
    border-color: var(--danger-color);
}

.button.danger:hover {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 55%) 0%,
        hsl(var(--danger-hue), 75%, 45%) 100%);
    box-shadow: 0 10px 25px hsla(var(--danger-hue), 75%, 60%, 0.3);
}

.button.outline {
    background: transparent;
    color: var(--primary-color);
}

.button.outline:hover {
    background: var(--primary-color);
    color: white;
}

.button.ghost {
    background: transparent;
    border: none;
    color: var(--text-secondary);
}

.button.ghost:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 5px var(--neon-cyan);
}

input, textarea, select {
    padding: 1rem 1.5rem;
    border: var(--border-neon);
    border-radius: 15px;
    background: var(--bg-card);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(191,0,255,0.05) 100%);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Rajdhani', sans-serif;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* Enhanced Features */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
    z-index: 1000;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-color);
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.primary {
    background: var(--gradient-primary);
    color: white;
}

.badge.secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.badge.success {
    background: var(--gradient-success);
    color: white;
}

.badge.warning {
    background: linear-gradient(135deg,
        hsl(var(--warning-hue), 85%, 60%) 0%,
        hsl(var(--warning-hue), 85%, 50%) 100%);
    color: var(--text-primary);
}

.badge.danger {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 60%) 0%,
        hsl(var(--danger-hue), 75%, 50%) 100%);
    color: white;
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 var(--space-md);
    }

    .grid-cols-4 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }

    .nav-container {
        flex-direction: column;
        gap: var(--space-md);
    }

    nav {
        justify-content: center;
        flex-wrap: wrap;
    }

    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    h1 {
        font-size: clamp(2rem, 4vw, 3rem);
    }
}

@media (max-width: 768px) {
    :root {
        --space-xs: 0.125rem;
        --space-sm: 0.375rem;
        --space-md: 0.75rem;
        --space-lg: 1.25rem;
        --space-xl: 1.75rem;
        --space-2xl: 2.5rem;
        --space-3xl: 3.5rem;
    }

    .container {
        padding: 0 var(--space-sm);
    }

    header {
        padding: var(--space-md) 0;
    }

    .header-content {
        gap: var(--space-md);
    }

    nav {
        gap: var(--space-sm);
    }

    nav a {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.875rem;
    }

    main {
        padding: var(--space-xl) 0;
    }

    .card, .journal-detail, .entry-card, .auth-container {
        padding: var(--space-lg);
        margin-bottom: var(--space-lg);
        border-radius: var(--radius-xl);
    }

    .grid-cols-2 {
        grid-template-columns: 1fr;
    }

    .grid {
        gap: var(--space-md);
    }

    button, .button, input[type="submit"] {
        padding: var(--space-sm) var(--space-lg);
        font-size: 0.875rem;
    }

    .button.large {
        padding: var(--space-md) var(--space-xl);
        font-size: 1rem;
    }

    input, textarea, select {
        padding: var(--space-sm) var(--space-md);
    }

    .theme-toggle {
        width: 50px;
        height: 25px;
    }

    .theme-toggle-slider {
        width: 19px;
        height: 19px;
    }

    [data-theme="light"] .theme-toggle-slider {
        transform: translateX(25px);
    }
}

@media (max-width: 640px) {
    .logo h1 {
        font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    }

    .logo-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .nav-container {
        gap: var(--space-sm);
    }

    .theme-toggle {
        width: 45px;
        height: 22px;
    }

    .theme-toggle-slider {
        width: 16px;
        height: 16px;
    }

    [data-theme="light"] .theme-toggle-slider {
        transform: translateX(23px);
    }

    .entry-image {
        height: 200px;
    }

    .modal-content {
        margin: var(--space-md);
        max-width: calc(100vw - 2rem);
    }

    .notification {
        top: var(--space-sm);
        right: var(--space-sm);
        left: var(--space-sm);
        max-width: none;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 var(--space-xs);
    }

    .logo {
        gap: var(--space-sm);
    }

    nav {
        gap: var(--space-xs);
    }

    nav a {
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.8rem;
    }

    .card, .journal-detail, .entry-card, .auth-container {
        padding: var(--space-md);
        border-radius: var(--radius-lg);
    }

    .entry-image {
        height: 180px;
        border-radius: var(--radius-lg);
    }

    button, .button, input[type="submit"] {
        padding: var(--space-xs) var(--space-md);
        font-size: 0.8rem;
        min-height: 36px;
    }

    .button.small {
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.75rem;
        min-height: 32px;
    }
}

/* Print Styles */
@media print {
    header, footer, .theme-toggle, .social-links {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card, .journal-detail, .entry-card {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }

    a {
        color: black !important;
        text-decoration: underline !important;
    }
}




