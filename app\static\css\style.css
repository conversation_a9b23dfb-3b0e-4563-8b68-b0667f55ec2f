/* Enhanced Modern Theme with Dark/Light Mode */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

:root {
    /* Core Colors */
    --primary-hue: 220;
    --secondary-hue: 280;
    --accent-hue: 200;
    --success-hue: 140;
    --warning-hue: 45;
    --danger-hue: 0;

    /* Light Theme */
    --bg-primary-light: hsl(var(--primary-hue), 15%, 98%);
    --bg-secondary-light: hsl(var(--primary-hue), 20%, 95%);
    --bg-tertiary-light: hsl(var(--primary-hue), 25%, 92%);
    --bg-card-light: hsl(var(--primary-hue), 30%, 99%);
    --text-primary-light: hsl(var(--primary-hue), 30%, 15%);
    --text-secondary-light: hsl(var(--primary-hue), 20%, 35%);
    --text-muted-light: hsl(var(--primary-hue), 15%, 55%);
    --border-light: hsl(var(--primary-hue), 20%, 85%);
    --shadow-light: 0 4px 20px hsla(var(--primary-hue), 30%, 0%, 0.1);

    /* Dark Theme */
    --bg-primary-dark: hsl(var(--primary-hue), 25%, 8%);
    --bg-secondary-dark: hsl(var(--primary-hue), 30%, 12%);
    --bg-tertiary-dark: hsl(var(--primary-hue), 35%, 16%);
    --bg-card-dark: hsl(var(--primary-hue), 40%, 10%);
    --text-primary-dark: hsl(var(--primary-hue), 20%, 95%);
    --text-secondary-dark: hsl(var(--primary-hue), 15%, 75%);
    --text-muted-dark: hsl(var(--primary-hue), 10%, 55%);
    --border-dark: hsl(var(--primary-hue), 25%, 25%);
    --shadow-dark: 0 8px 32px hsla(var(--primary-hue), 50%, 0%, 0.3);

    /* Accent Colors */
    --primary-color: hsl(var(--primary-hue), 85%, 60%);
    --secondary-color: hsl(var(--secondary-hue), 75%, 65%);
    --accent-color: hsl(var(--accent-hue), 80%, 55%);
    --success-color: hsl(var(--success-hue), 70%, 50%);
    --warning-color: hsl(var(--warning-hue), 85%, 60%);
    --danger-color: hsl(var(--danger-hue), 75%, 60%);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg,
        hsl(var(--primary-hue), 85%, 60%) 0%,
        hsl(var(--secondary-hue), 75%, 65%) 100%);
    --gradient-accent: linear-gradient(135deg,
        hsl(var(--accent-hue), 80%, 55%) 0%,
        hsl(var(--primary-hue), 85%, 60%) 100%);
    --gradient-success: linear-gradient(135deg,
        hsl(var(--success-hue), 70%, 50%) 0%,
        hsl(var(--accent-hue), 80%, 55%) 100%);

    /* Animation & Transitions */
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Enhanced Spacing Scale */
    --space-xs: 0.375rem;
    --space-sm: 0.75rem;
    --space-md: 1.25rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;
    --space-4xl: 8rem;
    --space-5xl: 12rem;

    /* Content Spacing */
    --content-spacing: var(--space-xl);
    --section-spacing: var(--space-3xl);
    --component-spacing: var(--space-lg);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* Theme Variables - Default to Dark */
:root {
    --bg-primary: var(--bg-primary-dark);
    --bg-secondary: var(--bg-secondary-dark);
    --bg-tertiary: var(--bg-tertiary-dark);
    --bg-card: var(--bg-card-dark);
    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-muted: var(--text-muted-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-dark);
}

/* Light Theme */
[data-theme="light"] {
    --bg-primary: var(--bg-primary-light);
    --bg-secondary: var(--bg-secondary-light);
    --bg-tertiary: var(--bg-tertiary-light);
    --bg-card: var(--bg-card-light);
    --text-primary: var(--text-primary-light);
    --text-secondary: var(--text-secondary-light);
    --text-muted: var(--text-muted-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-light);
}

/* Global Reset & Animations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes morphing {
    0%, 100% { border-radius: var(--radius-2xl); }
    25% { border-radius: var(--radius-full) var(--radius-2xl) var(--radius-full) var(--radius-2xl); }
    50% { border-radius: var(--radius-full); }
    75% { border-radius: var(--radius-2xl) var(--radius-full) var(--radius-2xl) var(--radius-full); }
}

@keyframes textShimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

@keyframes breathe {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes colorShift {
    0%, 100% { filter: hue-rotate(0deg); }
    25% { filter: hue-rotate(90deg); }
    50% { filter: hue-rotate(180deg); }
    75% { filter: hue-rotate(270deg); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
    25% { transform: translateY(-20px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-30px) rotate(270deg); opacity: 0.9; }
}

@keyframes liquidMove {
    0%, 100% { transform: translateX(0) translateY(0) scale(1); }
    25% { transform: translateX(20px) translateY(-10px) scale(1.1); }
    50% { transform: translateX(-15px) translateY(-20px) scale(0.9); }
    75% { transform: translateX(-25px) translateY(10px) scale(1.05); }
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, hsla(var(--primary-hue), 70%, 60%, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, hsla(var(--secondary-hue), 70%, 60%, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, hsla(var(--accent-hue), 70%, 60%, 0.02) 0%, transparent 50%);
    color: var(--text-primary);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    transition: var(--transition-smooth);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--content-spacing);
}

.container-narrow {
    max-width: 800px;
}

.container-wide {
    max-width: 1600px;
}

.container-fluid {
    max-width: none;
    padding: 0 var(--space-md);
}

/* Utility Classes */
.fade-in { animation: fadeIn 0.6s ease-out; }
.slide-up { animation: slideUp 0.6s ease-out; }
.slide-down { animation: slideDown 0.6s ease-out; }
.slide-left { animation: slideLeft 0.6s ease-out; }
.slide-right { animation: slideRight 0.6s ease-out; }
.pulse { animation: pulse 2s infinite; }
.glow { animation: glow 2s infinite; }
.float { animation: float 3s ease-in-out infinite; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--space-lg);
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-xl);
    position: relative;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: var(--radius-full);
    opacity: 0.7;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--primary-color);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2.25rem);
    font-weight: 600;
    color: var(--secondary-color);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.875rem);
    font-weight: 600;
}

h5 {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    font-weight: 600;
}

h6 {
    font-size: clamp(1rem, 1.5vw, 1.25rem);
    font-weight: 600;
}

p {
    margin-bottom: var(--space-md);
    font-weight: 400;
    color: var(--text-secondary);
    line-height: 1.7;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--text-secondary);
    line-height: 1.6;
}

.small {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.code {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.875rem;
    background: var(--bg-tertiary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-smooth);
    position: relative;
    font-weight: 500;
}

a:hover {
    color: var(--accent-color);
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
}

.link-underline {
    position: relative;
}

.link-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: width 0.3s ease;
}

.link-underline:hover::after {
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    padding: 2px;
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px hsla(var(--primary-hue), 85%, 60%, 0.1);
}

.theme-toggle-slider {
    width: 22px;
    height: 22px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-color);
}

.theme-toggle-slider i {
    font-size: 12px;
    color: white;
}

[data-theme="light"] .theme-toggle-slider {
    transform: translateX(30px);
}

/* Header */
header {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg,
        hsla(var(--primary-hue), 70%, 60%, 0.05) 0%,
        hsla(var(--secondary-hue), 70%, 60%, 0.05) 100%);
    padding: var(--space-lg) 0;
    box-shadow: var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
    transition: var(--transition-smooth);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideDown 0.8s ease-out;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo h1 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: -0.02em;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-accent);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    font-weight: 700;
    box-shadow: var(--shadow-color);
    transition: var(--transition-smooth);
}

.logo-icon:hover {
    transform: rotate(5deg) scale(1.05);
}

.nav-container {
    display: flex;
    align-items: center;
    gap: var(--space-xl);
}

nav {
    display: flex;
    gap: var(--space-lg);
    align-items: center;
}

nav a {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-full);
    border: 1px solid transparent;
    background: transparent;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    transition: left 0.3s ease;
    z-index: -1;
}

nav a:hover::before {
    left: 0;
}

nav a:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-color);
}

nav a i {
    font-size: 1rem;
    transition: var(--transition-smooth);
}

nav a:hover i {
    transform: scale(1.1);
}

/* Main Content */
main {
    padding: var(--space-5xl) 0 var(--space-4xl) 0;
    min-height: calc(100vh - 200px);
    animation: fadeIn 1s ease-out;
    margin-top: var(--space-xl);
}

.page-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
    padding: var(--space-2xl) 0;
    animation: slideUp 0.8s ease-out;
}

.page-title {
    margin-bottom: var(--space-lg);
    animation: slideUp 0.8s ease-out;
    padding-top: var(--space-xl);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1.25rem;
    margin-bottom: var(--space-3xl);
    animation: slideUp 0.8s ease-out 0.2s both;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Content Sections */
.content-section {
    margin-bottom: var(--space-4xl);
    padding: var(--space-2xl) 0;
}

.content-section:first-child {
    margin-top: var(--space-2xl);
}

.content-section:last-child {
    margin-bottom: var(--space-5xl);
}

/* Enhanced Cards & Content */
.card, .journal-detail, .entry-card, .auth-container {
    background: var(--bg-card);
    background-image: linear-gradient(135deg,
        hsla(var(--primary-hue), 70%, 60%, 0.02) 0%,
        hsla(var(--secondary-hue), 70%, 60%, 0.02) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    margin-bottom: var(--space-2xl);
    box-shadow: var(--shadow-color);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

/* Enhanced spacing for different card types */
.entry-card {
    margin-bottom: var(--space-3xl);
    padding: var(--space-2xl) var(--space-2xl) var(--space-3xl) var(--space-2xl);
}

.journal-detail {
    margin-top: var(--space-2xl);
    margin-bottom: var(--space-3xl);
    padding: var(--space-3xl);
}

.auth-container {
    margin-top: var(--space-4xl);
    margin-bottom: var(--space-4xl);
    padding: var(--space-3xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.card::before, .journal-detail::before, .entry-card::before, .auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, hsla(var(--primary-hue), 70%, 60%, 0.02) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover::before, .journal-detail:hover::before, .entry-card:hover::before {
    opacity: 1;
}

.card:hover, .journal-detail:hover, .entry-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 32px hsla(var(--primary-hue), 50%, 0%, 0.12);
    border-color: var(--primary-color);
}

.card:active, .journal-detail:active, .entry-card:active {
    transform: translateY(-2px);
    transition: var(--transition-fast);
}

.entry-card {
    display: flex;
    flex-direction: column;
    animation: slideUp 0.6s ease-out;
}

.entry-image {
    width: 100%;
    height: 280px;
    overflow: hidden;
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-xl);
    border: 2px solid var(--border-color);
    position: relative;
    transition: var(--transition-smooth);
    background: var(--bg-tertiary);
}

.entry-image:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px hsla(var(--primary-hue), 85%, 60%, 0.1);
}

.entry-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.entry-image:hover img {
    transform: scale(1.02);
    filter: brightness(1.05) contrast(1.1);
}

.entry-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, hsla(var(--primary-hue), 70%, 60%, 0.05) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.entry-image:hover::after {
    opacity: 1;
}

.entry-title {
    font-size: 1.75rem;
    margin-bottom: var(--space-lg);
    color: var(--primary-color);
    font-weight: 600;
    line-height: 1.3;
    padding-bottom: var(--space-sm);
}

.entry-date {
    color: var(--text-muted);
    font-size: 1rem;
    margin-bottom: var(--space-xl);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
    padding: var(--space-sm) 0;
}

.entry-date i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.entry-content {
    margin-bottom: var(--space-xl);
    line-height: 1.7;
    color: var(--text-secondary);
}

.entry-actions {
    display: flex;
    gap: var(--space-lg);
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-color);
    flex-wrap: wrap;
}

.entry-actions .button {
    transition: var(--transition-smooth);
}

.entry-actions .button:hover {
    transform: translateY(-2px) scale(1.05);
}

.entry-meta {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
    padding: var(--space-sm) 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.9rem;
}

.entry-meta .meta-item {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.entry-meta i {
    color: var(--accent-color);
}

/* Enhanced Journal Detail Styles */
.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-xl);
    padding: var(--space-md) 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-link {
    color: var(--text-secondary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    transition: var(--transition-smooth);
}

.breadcrumb-link:hover {
    color: var(--primary-color);
}

.breadcrumb-separator {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.breadcrumb-current {
    color: var(--text-primary);
    font-weight: 600;
}

.journal-header {
    margin-bottom: var(--space-3xl);
    position: relative;
}

.header-content {
    margin-bottom: var(--space-xl);
}

.entry-meta-enhanced {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg);
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
}

.entry-meta-enhanced .meta-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.entry-meta-enhanced i {
    color: var(--accent-color);
    width: 16px;
    text-align: center;
}

.reading-time, .last-updated {
    font-style: italic;
}

.quick-actions {
    display: flex;
    gap: var(--space-sm);
    position: absolute;
    top: 0;
    right: 0;
}

.action-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    background: var(--bg-card);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--bg-tertiary);
    transform: translateY(-2px) scale(1.05);
}

.action-btn.favorited {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Enhanced Media Section */
.entry-media {
    margin-bottom: var(--space-3xl);
}

.media-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-color);
}

.entry-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-xl);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-md);
    opacity: 0;
    transition: var(--transition-smooth);
}

.entry-image:hover .image-overlay {
    opacity: 1;
}

.overlay-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid white;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.overlay-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Enhanced Content Section */
.entry-content-wrapper {
    margin-bottom: var(--space-3xl);
}

.content-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    background: var(--bg-tertiary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    border: 1px solid var(--border-color);
    border-bottom: none;
}

.content-controls {
    display: flex;
    gap: var(--space-sm);
}

.control-btn {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    background: var(--bg-card);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.control-btn:hover, .control-btn.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--bg-primary);
}

.progress-indicator {
    width: 200px;
    height: 4px;
    background: var(--border-color);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: var(--radius-full);
}

.entry-content {
    padding: var(--space-2xl);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    line-height: 1.8;
    font-size: 1.1rem;
    transition: var(--transition-smooth);
}

.entry-content-wrapper.reading-mode .entry-content {
    max-width: 700px;
    margin: 0 auto;
    font-size: 1.2rem;
    line-height: 2;
    background: var(--bg-primary);
    color: var(--text-primary);
}

/* Fullscreen Mode */
.journal-detail.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: var(--bg-primary);
    padding: var(--space-xl);
    overflow-y: auto;
}

.journal-detail.fullscreen-mode .breadcrumb-nav,
.journal-detail.fullscreen-mode .quick-actions {
    display: none;
}

/* Enhanced Audio Section */
.entry-audio {
    margin-bottom: var(--space-3xl);
    background: var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    border: 1px solid var(--border-color);
}

.audio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.audio-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-primary);
}

.audio-controls {
    display: flex;
    gap: var(--space-sm);
}

.audio-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    background: var(--bg-card);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.audio-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--bg-primary);
}

.audio-player {
    position: relative;
}

.audio-player audio {
    width: 100%;
    margin-bottom: var(--space-md);
}

.audio-visualizer {
    display: flex;
    align-items: end;
    gap: 3px;
    height: 40px;
    justify-content: center;
}

.visualizer-bar {
    width: 4px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    animation: audioVisualize 1s ease-in-out infinite alternate;
}

.visualizer-bar:nth-child(1) { height: 20px; animation-delay: 0s; }
.visualizer-bar:nth-child(2) { height: 35px; animation-delay: 0.1s; }
.visualizer-bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }
.visualizer-bar:nth-child(4) { height: 40px; animation-delay: 0.3s; }
.visualizer-bar:nth-child(5) { height: 15px; animation-delay: 0.4s; }

@keyframes audioVisualize {
    0% { height: 10px; }
    100% { height: 40px; }
}

/* Enhanced Tags Section */
.entry-tags {
    margin-bottom: var(--space-3xl);
}

.entry-tags h4 {
    margin-bottom: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-primary);
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-md);
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full);
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.tag:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--bg-card);
    transform: translateY(-2px) scale(1.05);
}

.tag i {
    font-size: 0.75rem;
}

/* Enhanced Actions Section */
.entry-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
    margin-top: var(--space-3xl);
    padding-top: var(--space-xl);
    border-top: 2px solid var(--border-color);
}

.primary-actions, .secondary-actions {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.primary-actions {
    justify-content: center;
}

.secondary-actions {
    justify-content: space-between;
}

/* Entry Statistics */
.entry-stats {
    background: var(--bg-tertiary);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    margin-top: var(--space-xl);
    border: 1px solid var(--border-color);
    animation: slideUp 0.3s ease-out;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-lg);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--space-lg);
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-smooth);
}

.stat-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--accent-color);
    margin-bottom: var(--space-sm);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--space-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Notification System */
.notification {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-lg);
    background: var(--bg-card);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-color);
    z-index: 1000;
    transform: translateX(100%);
    transition: var(--transition-smooth);
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-color: var(--success-color);
    background: linear-gradient(135deg,
        hsla(var(--success-hue), 70%, 50%, 0.1) 0%,
        hsla(var(--success-hue), 70%, 50%, 0.05) 100%);
    color: var(--success-color);
}

.notification.error {
    border-color: var(--danger-color);
    background: linear-gradient(135deg,
        hsla(var(--danger-hue), 75%, 60%, 0.1) 0%,
        hsla(var(--danger-hue), 75%, 60%, 0.05) 100%);
    color: var(--danger-color);
}

.notification.info {
    border-color: var(--accent-color);
    background: linear-gradient(135deg,
        hsla(var(--accent-hue), 80%, 55%, 0.1) 0%,
        hsla(var(--accent-hue), 80%, 55%, 0.05) 100%);
    color: var(--accent-color);
}

/* Footer */
footer {
    background: var(--bg-secondary);
    background-image: linear-gradient(135deg,
        hsla(var(--secondary-hue), 70%, 60%, 0.05) 0%,
        hsla(var(--primary-hue), 70%, 60%, 0.05) 100%);
    padding: var(--space-2xl) 0;
    margin-top: var(--space-3xl);
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--space-lg);
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: var(--space-lg);
}

.social-links a {
    color: var(--text-secondary);
    font-size: 1.25rem;
    padding: var(--space-md);
    border-radius: 50%;
    border: 2px solid var(--border-color);
    background: var(--bg-tertiary);
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
}

.social-links a:hover {
    color: var(--primary-color);
    background: var(--bg-card);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-color);
}

/* Enhanced Forms */
form {
    display: flex;
    flex-direction: column;
    gap: var(--space-2xl);
    margin-top: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: var(--space-sm);
    display: block;
}

input, textarea, select {
    padding: var(--space-lg) var(--space-xl);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Inter', sans-serif;
    transition: var(--transition-smooth);
    backdrop-filter: blur(10px);
    min-height: 48px;
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
}

.form-section {
    margin-bottom: var(--space-3xl);
    padding-bottom: var(--space-xl);
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px hsla(var(--primary-hue), 85%, 60%, 0.15);
    background: var(--bg-card);
    transform: translateY(-1px);
}

input:hover, textarea:hover, select:hover {
    border-color: var(--accent-color);
    background: var(--bg-card);
}

textarea {
    resize: vertical;
    min-height: 120px;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

.form-error {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.form-success {
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.form-help {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-top: var(--space-xs);
}

/* Buttons */
button, .button, input[type="submit"] {
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-full);
    border: 2px solid var(--primary-color);
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    font-family: 'Inter', sans-serif;
    min-height: 44px;
}

button::before, .button::before, input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

button:hover::before, .button:hover::before, input[type="submit"]:hover::before {
    left: 100%;
}

button:hover, .button:hover, input[type="submit"]:hover {
    background: var(--gradient-accent);
    border-color: var(--accent-color);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px hsla(var(--primary-hue), 85%, 60%, 0.25);
}

button:active, .button:active, input[type="submit"]:active {
    transform: translateY(0) scale(0.98);
    transition: var(--transition-fast);
}

button:focus, .button:focus, input[type="submit"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px hsla(var(--primary-hue), 85%, 60%, 0.3);
}

button:disabled, .button:disabled, input[type="submit"]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

button:disabled:hover, .button:disabled:hover, input[type="submit"]:disabled:hover {
    background: var(--gradient-primary);
    transform: none;
    box-shadow: none;
}

.button.small {
    padding: var(--space-sm) var(--space-lg);
    font-size: 0.875rem;
    min-height: 36px;
}

.button.large {
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1.125rem;
    min-height: 52px;
}

.button.secondary {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.button.secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.button.success {
    background: var(--gradient-success);
    border-color: var(--success-color);
}

.button.success:hover {
    background: linear-gradient(135deg,
        hsl(var(--success-hue), 70%, 45%) 0%,
        hsl(var(--accent-hue), 80%, 50%) 100%);
}

.button.warning {
    background: linear-gradient(135deg,
        hsl(var(--warning-hue), 85%, 60%) 0%,
        hsl(var(--warning-hue), 85%, 50%) 100%);
    border-color: var(--warning-color);
    color: var(--text-primary);
}

.button.danger {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 60%) 0%,
        hsl(var(--danger-hue), 75%, 50%) 100%);
    border-color: var(--danger-color);
}

.button.danger:hover {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 55%) 0%,
        hsl(var(--danger-hue), 75%, 45%) 100%);
    box-shadow: 0 10px 25px hsla(var(--danger-hue), 75%, 60%, 0.3);
}

.button.outline {
    background: transparent;
    color: var(--primary-color);
}

.button.outline:hover {
    background: var(--primary-color);
    color: white;
}

.button.ghost {
    background: transparent;
    border: none;
    color: var(--text-secondary);
}

.button.ghost:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Forms */
form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 5px var(--neon-cyan);
}

input, textarea, select {
    padding: 1rem 1.5rem;
    border: var(--border-neon);
    border-radius: 15px;
    background: var(--bg-card);
    background-image: linear-gradient(135deg, rgba(0,255,255,0.05) 0%, rgba(191,0,255,0.05) 100%);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: 'Rajdhani', sans-serif;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* Enhanced Features */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
    z-index: 1000;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-color);
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge.primary {
    background: var(--gradient-primary);
    color: white;
}

.badge.secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.badge.success {
    background: var(--gradient-success);
    color: white;
}

.badge.warning {
    background: linear-gradient(135deg,
        hsl(var(--warning-hue), 85%, 60%) 0%,
        hsl(var(--warning-hue), 85%, 50%) 100%);
    color: var(--text-primary);
}

.badge.danger {
    background: linear-gradient(135deg,
        hsl(var(--danger-hue), 75%, 60%) 0%,
        hsl(var(--danger-hue), 75%, 50%) 100%);
    color: white;
}

/* Enhanced Layout Utilities */
.container-spaced {
    padding-top: var(--space-4xl);
    padding-bottom: var(--space-4xl);
}

.section-divider {
    margin: var(--space-4xl) 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.content-wrapper {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--space-2xl);
}

.hero-section {
    padding: var(--space-5xl) 0;
    text-align: center;
    margin-bottom: var(--space-4xl);
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-3xl);
    margin: var(--space-4xl) 0;
}

.journal-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-2xl);
    margin-top: var(--space-3xl);
}

.entry-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--space-3xl);
    margin-top: var(--space-2xl);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 var(--space-md);
    }

    .grid-cols-4 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1024px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-lg);
        text-align: center;
    }

    .nav-container {
        flex-direction: column;
        gap: var(--space-md);
    }

    nav {
        justify-content: center;
        flex-wrap: wrap;
    }

    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    h1 {
        font-size: clamp(2rem, 4vw, 3rem);
    }
}

@media (max-width: 768px) {
    :root {
        --space-xs: 0.25rem;
        --space-sm: 0.5rem;
        --space-md: 1rem;
        --space-lg: 1.5rem;
        --space-xl: 2rem;
        --space-2xl: 3rem;
        --space-3xl: 4rem;
        --space-4xl: 5rem;
        --space-5xl: 7rem;
    }

    .container {
        padding: 0 var(--space-md);
    }

    header {
        padding: var(--space-lg) 0;
    }

    .header-content {
        gap: var(--space-lg);
    }

    nav {
        gap: var(--space-sm);
    }

    nav a {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.875rem;
    }

    main {
        padding: var(--space-3xl) 0 var(--space-2xl) 0;
        margin-top: var(--space-lg);
    }

    .page-title {
        padding-top: var(--space-lg);
        margin-bottom: var(--space-lg);
    }

    .page-subtitle {
        margin-bottom: var(--space-2xl);
    }

    .card, .journal-detail, .entry-card, .auth-container {
        padding: var(--space-xl);
        margin-bottom: var(--space-xl);
        border-radius: var(--radius-xl);
    }

    .entry-card {
        margin-bottom: var(--space-2xl);
        padding: var(--space-xl);
    }

    .journal-detail {
        padding: var(--space-2xl);
        margin-top: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .auth-container {
        margin-top: var(--space-2xl);
        margin-bottom: var(--space-2xl);
        padding: var(--space-2xl);
    }

    .journal-list, .entry-list {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        margin-top: var(--space-xl);
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        margin: var(--space-2xl) 0;
    }

    button, .button, input[type="submit"] {
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    .button.large {
        padding: var(--space-lg) var(--space-xl);
        font-size: 1rem;
    }

    input, textarea, select {
        padding: var(--space-md) var(--space-lg);
    }

    .theme-toggle {
        width: 50px;
        height: 25px;
    }

    .theme-toggle-slider {
        width: 19px;
        height: 19px;
    }

    [data-theme="light"] .theme-toggle-slider {
        transform: translateX(25px);
    }
}

@media (max-width: 640px) {
    .logo h1 {
        font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    }

    .logo-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .nav-container {
        gap: var(--space-sm);
    }

    .theme-toggle {
        width: 45px;
        height: 22px;
    }

    .theme-toggle-slider {
        width: 16px;
        height: 16px;
    }

    [data-theme="light"] .theme-toggle-slider {
        transform: translateX(23px);
    }

    .entry-image {
        height: 200px;
    }

    .modal-content {
        margin: var(--space-md);
        max-width: calc(100vw - 2rem);
    }

    .notification {
        top: var(--space-sm);
        right: var(--space-sm);
        left: var(--space-sm);
        max-width: none;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 var(--space-xs);
    }

    .logo {
        gap: var(--space-sm);
    }

    nav {
        gap: var(--space-xs);
    }

    nav a {
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.8rem;
    }

    .card, .journal-detail, .entry-card, .auth-container {
        padding: var(--space-md);
        border-radius: var(--radius-lg);
    }

    .entry-image {
        height: 180px;
        border-radius: var(--radius-lg);
    }

    button, .button, input[type="submit"] {
        padding: var(--space-xs) var(--space-md);
        font-size: 0.8rem;
        min-height: 36px;
    }

    .button.small {
        padding: var(--space-xs) var(--space-sm);
        font-size: 0.75rem;
        min-height: 32px;
    }
}

/* Print Styles */
@media print {
    header, footer, .theme-toggle, .social-links {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .card, .journal-detail, .entry-card {
        background: white !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }

    a {
        color: black !important;
        text-decoration: underline !important;
    }
}




