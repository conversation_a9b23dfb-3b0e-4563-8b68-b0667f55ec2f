{% extends 'app/base.html' %}

{% block content %}
<div class="day-view-container">
    <div class="day-header">
        <div class="day-navigation">
            <a href="{% url 'calendar_view' %}" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Calendar
            </a>
            <h2 class="day-title">
                <i class="fas fa-calendar-day animated-icon"></i>
                {{ selected_date|date:"F j, Y" }}
                <span class="day-name">({{ selected_date|date:"l" }})</span>
            </h2>
        </div>
        
        <div class="day-actions">
            <a href="{% url 'journal_new' %}?date={{ selected_date|date:'Y-m-d' }}" class="button primary-btn">
                <i class="fas fa-plus animated-icon"></i> New Entry for This Day
            </a>
        </div>
    </div>

    <div class="day-content">
        {% if entries %}
            <div class="entries-section">
                <h3 class="section-title">
                    <i class="fas fa-book-open"></i> 
                    Journal Entries ({{ entries|length }})
                </h3>
                
                <div class="day-entries">
                    {% for entry in entries %}
                    <div class="day-entry-card">
                        <div class="entry-header">
                            <h4 class="entry-title">{{ entry.title }}</h4>
                            <div class="entry-time">
                                <i class="far fa-clock"></i>
                                {{ entry.created_at|time:"g:i A" }}
                            </div>
                        </div>
                        
                        {% if entry.image %}
                        <div class="entry-image">
                            <img src="{{ entry.image.url }}" alt="{{ entry.title }}" onclick="openModal('{{ entry.image.url }}')">
                        </div>
                        {% endif %}
                        
                        <div class="entry-content">
                            <p>{{ entry.content|truncatewords:30|linebreaks }}</p>
                        </div>
                        
                        <div class="entry-meta">
                            {% if entry.category %}
                            <span class="entry-category">
                                <i class="fas fa-folder"></i> {{ entry.category }}
                            </span>
                            {% endif %}
                            
                            {% if entry.tags %}
                            <div class="entry-tags">
                                <i class="fas fa-tags"></i>
                                {% for tag in entry.tags|split:"," %}
                                    <span class="tag">{{ tag|trim }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="entry-actions">
                            <a href="{% url 'journal_detail' pk=entry.pk %}" class="button small">
                                <i class="fas fa-eye"></i> View Full
                            </a>
                            <a href="{% url 'journal_edit' pk=entry.pk %}" class="button small secondary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="empty-day">
                <div class="empty-icon">
                    <i class="fas fa-calendar-plus"></i>
                </div>
                <h3>No entries for this day</h3>
                <p>Start journaling about your day! Capture your thoughts, experiences, and memories.</p>
                <a href="{% url 'journal_new' %}?date={{ selected_date|date:'Y-m-d' }}" class="button primary-btn pulse-button">
                    <i class="fas fa-plus"></i> Create Your First Entry
                </a>
            </div>
        {% endif %}
        
        <!-- Day Statistics -->
        <div class="day-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-book"></i>
                </div>
                <div class="stat-info">
                    <h4>{{ entries|length }}</h4>
                    <p>Entries</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-font"></i>
                </div>
                <div class="stat-info">
                    <h4>{{ total_words|default:0 }}</h4>
                    <p>Words</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <h4>{{ selected_date|date:"l" }}</h4>
                    <p>Day of Week</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="modal">
    <div class="modal-content image-modal">
        <span class="close-modal">&times;</span>
        <img id="modalImage" src="" alt="">
    </div>
</div>

<style>
.day-view-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.day-navigation {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.back-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: var(--accent-color);
    transform: translateX(-5px);
}

.day-title {
    font-size: 2rem;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.day-name {
    font-size: 1.2rem;
    color: var(--text-color);
    opacity: 0.7;
    font-weight: normal;
}

.day-content {
    display: grid;
    gap: 30px;
}

.section-title {
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
}

.day-entries {
    display: grid;
    gap: 20px;
}

.day-entry-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid var(--accent-color);
}

.day-entry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.entry-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.entry-title {
    color: var(--primary-color);
    margin: 0;
    font-size: 1.3rem;
    flex: 1;
}

.entry-time {
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.entry-image {
    margin: 15px 0;
    text-align: center;
}

.entry-image img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.entry-image img:hover {
    transform: scale(1.05);
}

.entry-content {
    margin: 15px 0;
    line-height: 1.6;
    color: var(--text-color);
}

.entry-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.entry-category {
    color: var(--primary-color);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.entry-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    background: var(--accent-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.entry-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.empty-day {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    opacity: 0.7;
}

.empty-day h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.empty-day p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 30px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.day-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-info h4 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-info p {
    margin: 0;
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Image Modal */
.image-modal {
    background: transparent;
    border: none;
    box-shadow: none;
    max-width: 90%;
    max-height: 90%;
}

.image-modal img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .day-header {
        flex-direction: column;
        text-align: center;
    }
    
    .day-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 5px;
    }
    
    .entry-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .day-stats {
        grid-template-columns: 1fr;
    }
    
    .entry-actions {
        justify-content: center;
    }
}
</style>

<script>
// Calculate total words for the day
document.addEventListener('DOMContentLoaded', function() {
    const entryContents = document.querySelectorAll('.entry-content p');
    let totalWords = 0;
    
    entryContents.forEach(content => {
        const text = content.textContent || content.innerText;
        const words = text.trim().split(/\s+/).filter(word => word.length > 0);
        totalWords += words.length;
    });
    
    // Update the words stat if it exists
    const wordsStatElement = document.querySelector('.stat-info h4');
    if (wordsStatElement && wordsStatElement.textContent === '0') {
        wordsStatElement.textContent = totalWords;
    }
});

// Image modal functionality
function openModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    modal.style.display = 'block';
    modalImg.src = imageSrc;
}

// Close modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('imageModal');
    const closeBtn = document.querySelector('.close-modal');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });
    }
    
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});
</script>

{% load custom_filters %}
{% endblock %}
