{% extends 'app/base.html' %}

{% block title %}{{ entry.title }} - Personal Journal{% endblock %}

{% block content %}
<div class="content-section">
    <div class="journal-detail fade-in">
        <!-- Enhanced <PERSON><PERSON> with Breadcrumbs -->
        <div class="breadcrumb-nav slide-right">
            <a href="{% url 'journal_list' %}" class="breadcrumb-link">
                <i class="fas fa-book"></i> My Journal
            </a>
            <i class="fas fa-chevron-right breadcrumb-separator"></i>
            <span class="breadcrumb-current">{{ entry.title|truncatechars:30 }}</span>
        </div>

        <div class="journal-header slide-up">
            <div class="header-content">
                <h1 class="entry-title">{{ entry.title }}</h1>
                <div class="entry-meta-enhanced">
                    <div class="meta-item">
                        <i class="far fa-calendar-alt"></i>
                        <span>{{ entry.created_at|date:"F j, Y \a\t g:i A" }}</span>
                    </div>
                    {% if entry.category %}
                    <div class="meta-item">
                        <i class="fas fa-folder"></i>
                        <span class="badge secondary">{{ entry.category }}</span>
                    </div>
                    {% endif %}
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span class="reading-time">{{ entry.content|wordcount|floatformat:0 }} words • ~{{ entry.content|wordcount|add:200|div:200|floatformat:0 }} min read</span>
                    </div>
                    {% if entry.updated_at != entry.created_at %}
                    <div class="meta-item">
                        <i class="fas fa-edit"></i>
                        <span class="last-updated">Updated {{ entry.updated_at|timesince }} ago</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions Toolbar -->
            <div class="quick-actions">
                <button class="action-btn" onclick="toggleFavorite()" title="Add to Favorites">
                    <i class="far fa-heart"></i>
                </button>
                <button class="action-btn" onclick="shareEntry()" title="Share Entry">
                    <i class="fas fa-share-alt"></i>
                </button>
                <button class="action-btn" onclick="printEntry()" title="Print Entry">
                    <i class="fas fa-print"></i>
                </button>
                <button class="action-btn" onclick="toggleFullscreen()" title="Focus Mode">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </div>

        <!-- Enhanced Image Section -->
        {% if entry.image %}
        <div class="entry-media slide-up">
            <div class="media-container">
                <div class="entry-image">
                    <img src="{{ entry.image.url }}" alt="{{ entry.title }}" loading="lazy">
                    <div class="image-overlay">
                        <button class="overlay-btn" onclick="viewFullImage('{{ entry.image.url }}')" title="View Full Size">
                            <i class="fas fa-expand"></i>
                        </button>
                        <button class="overlay-btn" onclick="downloadImage('{{ entry.image.url }}')" title="Download">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Content Section -->
        <div class="entry-content-wrapper slide-up">
            <div class="content-toolbar">
                <div class="content-controls">
                    <button class="control-btn" onclick="adjustFontSize('increase')" title="Increase Font Size">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="control-btn" onclick="adjustFontSize('decrease')" title="Decrease Font Size">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="control-btn" onclick="toggleReadingMode()" title="Reading Mode">
                        <i class="fas fa-book-open"></i>
                    </button>
                    <button class="control-btn" onclick="copyContent()" title="Copy Content">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="progress-indicator">
                    <div class="progress-bar" id="readingProgress"></div>
                </div>
            </div>

            <article class="entry-content" id="entryContent">
                {{ entry.content|linebreaks }}
            </article>
        </div>

        <!-- Enhanced Audio Section -->
        {% if entry.audio %}
        <div class="entry-audio slide-up">
            <div class="audio-header">
                <h4><i class="fas fa-headphones"></i> Audio Recording</h4>
                <div class="audio-controls">
                    <button class="audio-btn" onclick="togglePlayback()" id="playBtn">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="audio-btn" onclick="adjustSpeed()" id="speedBtn">
                        1x
                    </button>
                    <button class="audio-btn" onclick="downloadAudio('{{ entry.audio.url }}')" title="Download Audio">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="audio-player">
                <audio controls id="audioPlayer">
                    <source src="{{ entry.audio.url }}" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>
                <div class="audio-visualizer">
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                    <div class="visualizer-bar"></div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Tags Section -->
        {% if entry.tags %}
        <div class="entry-tags slide-up">
            <h4><i class="fas fa-tags"></i> Tags</h4>
            <div class="tags-container">
                {% for tag in entry.tags.split %}
                <span class="tag" onclick="searchByTag('{{ tag }}')">
                    <i class="fas fa-tag"></i> {{ tag }}
                </span>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Actions Section -->
        <div class="entry-actions slide-up">
            <div class="primary-actions">
                <a href="{% url 'journal_edit' pk=entry.pk %}" class="button primary">
                    <i class="fas fa-edit"></i> Edit Entry
                </a>
                <button class="button secondary" onclick="duplicateEntry()">
                    <i class="fas fa-copy"></i> Duplicate
                </button>
                <button class="button success" onclick="exportEntry()">
                    <i class="fas fa-download"></i> Export
                </button>
            </div>

            <div class="secondary-actions">
                <a href="{% url 'journal_list' %}" class="button outline">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
                <button class="button ghost" onclick="showEntryStats()">
                    <i class="fas fa-chart-bar"></i> Stats
                </button>
                <a href="{% url 'journal_delete' pk=entry.pk %}" class="button danger" onclick="return confirmDelete()">
                    <i class="fas fa-trash"></i> Delete
                </a>
            </div>
        </div>

        <!-- Entry Statistics Panel -->
        <div class="entry-stats" id="entryStats" style="display: none;">
            <div class="stats-grid">
                <div class="stat-item">
                    <i class="fas fa-font"></i>
                    <span class="stat-value">{{ entry.content|wordcount }}</span>
                    <span class="stat-label">Words</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-paragraph"></i>
                    <span class="stat-value">{{ entry.content|linebreaksbr|length }}</span>
                    <span class="stat-label">Characters</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span class="stat-value">{{ entry.content|wordcount|add:200|div:200|floatformat:0 }}</span>
                    <span class="stat-label">Min Read</span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-calendar"></i>
                    <span class="stat-value">{{ entry.created_at|timesince }}</span>
                    <span class="stat-label">Age</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced JavaScript Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Reading progress indicator
    const progressBar = document.getElementById('readingProgress');
    const content = document.getElementById('entryContent');

    if (progressBar && content) {
        window.addEventListener('scroll', updateReadingProgress);
    }

    // Auto-save scroll position
    const scrollPosition = localStorage.getItem('entryScrollPosition');
    if (scrollPosition) {
        window.scrollTo(0, parseInt(scrollPosition));
        localStorage.removeItem('entryScrollPosition');
    }
});

function updateReadingProgress() {
    const content = document.getElementById('entryContent');
    const progressBar = document.getElementById('readingProgress');

    if (!content || !progressBar) return;

    const contentTop = content.offsetTop;
    const contentHeight = content.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;

    const progress = Math.min(100, Math.max(0,
        ((scrollTop - contentTop + windowHeight) / contentHeight) * 100
    ));

    progressBar.style.width = progress + '%';
}

function toggleFavorite() {
    const btn = event.target.closest('.action-btn');
    const icon = btn.querySelector('i');

    if (icon.classList.contains('far')) {
        icon.classList.remove('far');
        icon.classList.add('fas');
        btn.classList.add('favorited');
        showNotification('Added to favorites', 'success');
    } else {
        icon.classList.remove('fas');
        icon.classList.add('far');
        btn.classList.remove('favorited');
        showNotification('Removed from favorites', 'info');
    }
}

function shareEntry() {
    if (navigator.share) {
        navigator.share({
            title: document.querySelector('.entry-title').textContent,
            text: 'Check out this journal entry',
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href);
        showNotification('Link copied to clipboard', 'success');
    }
}

function printEntry() {
    window.print();
}

function toggleFullscreen() {
    const content = document.querySelector('.journal-detail');
    content.classList.toggle('fullscreen-mode');

    const btn = event.target.closest('.action-btn');
    const icon = btn.querySelector('i');

    if (content.classList.contains('fullscreen-mode')) {
        icon.classList.remove('fa-expand');
        icon.classList.add('fa-compress');
        btn.title = 'Exit Focus Mode';
    } else {
        icon.classList.remove('fa-compress');
        icon.classList.add('fa-expand');
        btn.title = 'Focus Mode';
    }
}

function adjustFontSize(action) {
    const content = document.getElementById('entryContent');
    const currentSize = parseInt(window.getComputedStyle(content).fontSize);

    if (action === 'increase' && currentSize < 24) {
        content.style.fontSize = (currentSize + 2) + 'px';
    } else if (action === 'decrease' && currentSize > 12) {
        content.style.fontSize = (currentSize - 2) + 'px';
    }

    localStorage.setItem('preferredFontSize', content.style.fontSize);
}

function toggleReadingMode() {
    const wrapper = document.querySelector('.entry-content-wrapper');
    wrapper.classList.toggle('reading-mode');

    const btn = event.target.closest('.control-btn');
    btn.classList.toggle('active');
}

function copyContent() {
    const content = document.getElementById('entryContent').innerText;
    navigator.clipboard.writeText(content).then(() => {
        showNotification('Content copied to clipboard', 'success');
    });
}

function showEntryStats() {
    const stats = document.getElementById('entryStats');
    stats.style.display = stats.style.display === 'none' ? 'block' : 'none';
}

function confirmDelete() {
    return confirm('⚠️ Are you sure you want to delete this entry?\n\nThis action cannot be undone.');
}

function duplicateEntry() {
    showNotification('Duplicating entry...', 'info');
    // Implementation would depend on backend
}

function exportEntry() {
    const title = document.querySelector('.entry-title').textContent;
    const content = document.getElementById('entryContent').innerText;
    const date = new Date().toISOString().split('T')[0];

    const exportData = `# ${title}\n\nDate: ${date}\n\n${content}`;

    const blob = new Blob([exportData], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification('Entry exported successfully', 'success');
}

function searchByTag(tag) {
    localStorage.setItem('searchTag', tag);
    window.location.href = '{% url "journal_list" %}?tag=' + encodeURIComponent(tag);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type} show`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
        ${message}
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Load saved preferences
document.addEventListener('DOMContentLoaded', function() {
    const savedFontSize = localStorage.getItem('preferredFontSize');
    if (savedFontSize) {
        document.getElementById('entryContent').style.fontSize = savedFontSize;
    }
});
</script>
{% endblock %}




