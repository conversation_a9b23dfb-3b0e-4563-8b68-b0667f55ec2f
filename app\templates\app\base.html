<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Personal Journal{% endblock %}</title>

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Main Styles -->
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Favicon -->
    <link rel="icon" href="/static/images/favicon.ico">

    <!-- Meta tags for SEO -->
    <meta name="description" content="Personal Journal - Capture your thoughts, memories, and inspirations">
    <meta name="keywords" content="journal, diary, personal, thoughts, memories">
    <meta name="author" content="Personal Journal App">

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#1a1a2e">

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Add floating particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Add professional gradient accents -->
    <div class="gradient-accent" style="background: linear-gradient(135deg, #6a11cb, #2575fc);"></div>

    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h1>Personal Journal</h1>
                </div>
                <div class="nav-container">
                    <nav>
                        {% if user.is_authenticated %}
                        <a href="{% url 'journal_list' %}"><i class="fas fa-book"></i> My Journal</a>
                        <a href="{% url 'calendar_view' %}"><i class="fas fa-calendar-alt"></i> Calendar</a>
                        <a href="{% url 'template_list' %}"><i class="fas fa-file-alt"></i> Templates</a>
                        <a href="{% url 'reminder_list' %}"><i class="fas fa-bell"></i> Reminders</a>
                        <a href="{% url 'logout' %}"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        {% else %}
                        <a href="{% url 'login' %}"><i class="fas fa-sign-in-alt"></i> Login</a>
                        <a href="{% url 'signup' %}"><i class="fas fa-user-plus"></i> Sign Up</a>
                        {% endif %}
                    </nav>
                    <!-- Theme toggle will be added by JavaScript -->
                </div>
            </div>
        </div>
    </header>

    <main class="container">
        {% block content %}
        {% endblock %}
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Personal Journal App</p>
                <div class="social-links">
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                </div>
                <p>Capture your thoughts, memories, and inspirations</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/static/js/theme-toggle.js"></script>

    <script>

        // Add loading states to forms
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                    }
                });
            });
        });

        // Add smooth scroll behavior
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>



